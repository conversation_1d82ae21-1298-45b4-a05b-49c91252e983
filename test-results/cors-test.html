<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CORS跨域请求测试</h1>
        <p>这个页面用于测试智慧养鹅平台后台管理API的跨域访问功能。</p>
        
        <div id="test1" class="test-section">
            <h3>测试1: 获取统计数据</h3>
            <button onclick="testStatistics()">测试 GET /api/admin/statistics</button>
            <div class="status" id="status1">点击按钮开始测试</div>
            <pre id="result1"></pre>
        </div>

        <div id="test2" class="test-section">
            <h3>测试2: 获取租户列表</h3>
            <button onclick="testTenants()">测试 GET /api/admin/tenants</button>
            <div class="status" id="status2">点击按钮开始测试</div>
            <pre id="result2"></pre>
        </div>

        <div id="test3" class="test-section">
            <h3>测试3: 获取待审批列表</h3>
            <button onclick="testPending()">测试 GET /api/admin/pending</button>
            <div class="status" id="status3">点击按钮开始测试</div>
            <pre id="result3"></pre>
        </div>

        <div id="test4" class="test-section">
            <h3>测试4: 创建租户 (POST请求)</h3>
            <button onclick="testCreateTenant()">测试 POST /api/admin/tenants</button>
            <div class="status" id="status4">点击按钮开始测试</div>
            <pre id="result4"></pre>
        </div>

        <div class="test-section">
            <h3>测试结果汇总</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="summary" class="status">等待测试...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4001/api/admin';
        
        async function testStatistics() {
            await runTest('test1', 'status1', 'result1', async () => {
                const response = await fetch(`${API_BASE}/statistics`);
                const data = await response.json();
                return {
                    status: response.status,
                    success: data.success,
                    data: data.data
                };
            });
        }

        async function testTenants() {
            await runTest('test2', 'status2', 'result2', async () => {
                const response = await fetch(`${API_BASE}/tenants?page=1&limit=5`);
                const data = await response.json();
                return {
                    status: response.status,
                    success: data.success,
                    count: data.data?.list?.length || 0,
                    total: data.data?.pagination?.total || 0
                };
            });
        }

        async function testPending() {
            await runTest('test3', 'status3', 'result3', async () => {
                const response = await fetch(`${API_BASE}/pending`);
                const data = await response.json();
                return {
                    status: response.status,
                    success: data.success,
                    count: data.data?.list?.length || 0,
                    total: data.data?.total || 0
                };
            });
        }

        async function testCreateTenant() {
            await runTest('test4', 'status4', 'result4', async () => {
                const testData = {
                    name: 'CORS测试农场',
                    tenant_code: 'cors_test_farm_' + Date.now(),
                    contact_name: 'CORS测试联系人',
                    contact_phone: '13800138000',
                    contact_email: '<EMAIL>',
                    subscription_plan: 'basic'
                };

                const response = await fetch(`${API_BASE}/tenants`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                // 如果创建成功，立即删除测试数据
                if (data.success && data.data?.id) {
                    try {
                        await fetch(`${API_BASE}/tenants/${data.data.id}`, {
                            method: 'DELETE'
                        });
                    } catch (e) {
                        console.log('清理测试数据失败:', e);
                    }
                }
                
                return {
                    status: response.status,
                    success: data.success,
                    created_id: data.data?.id,
                    message: data.message
                };
            });
        }

        async function runTest(testId, statusId, resultId, testFunction) {
            const testElement = document.getElementById(testId);
            const statusElement = document.getElementById(statusId);
            const resultElement = document.getElementById(resultId);
            
            // 设置加载状态
            testElement.className = 'test-section loading';
            statusElement.textContent = '测试中...';
            resultElement.textContent = '';
            
            try {
                const result = await testFunction();
                
                // 设置成功状态
                testElement.className = 'test-section success';
                statusElement.textContent = '✅ 测试通过';
                resultElement.textContent = JSON.stringify(result, null, 2);
                
                return true;
            } catch (error) {
                // 设置错误状态
                testElement.className = 'test-section error';
                statusElement.textContent = '❌ 测试失败';
                resultElement.textContent = `错误信息: ${error.message}\n\n详细信息:\n${error.stack || error}`;
                
                return false;
            }
        }

        async function runAllTests() {
            const summaryElement = document.getElementById('summary');
            summaryElement.textContent = '正在运行所有测试...';
            
            const tests = [
                { name: '统计数据', func: testStatistics },
                { name: '租户列表', func: testTenants },
                { name: '待审批列表', func: testPending },
                { name: '创建租户', func: testCreateTenant }
            ];
            
            let passed = 0;
            let total = tests.length;
            
            for (const test of tests) {
                try {
                    const result = await test.func();
                    if (result) passed++;
                } catch (e) {
                    console.error(`测试 ${test.name} 失败:`, e);
                }
                
                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            summaryElement.innerHTML = `
                <strong>测试完成!</strong><br>
                通过: ${passed}/${total}<br>
                成功率: ${((passed/total)*100).toFixed(1)}%<br>
                ${passed === total ? '🎉 所有测试通过！CORS配置正常。' : '⚠️ 部分测试失败，请检查CORS配置。'}
            `;
        }

        // 页面加载完成后自动运行一次基础测试
        window.addEventListener('load', () => {
            console.log('CORS测试页面已加载');
            console.log('API基础地址:', API_BASE);
        });
    </script>
</body>
</html>
