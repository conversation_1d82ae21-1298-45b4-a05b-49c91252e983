{"config": {"configFile": "/Volumes/DATA/千问/智慧养鹅全栈/playwright.config.js", "rootDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-setup.js", "globalTeardown": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/playwright-report"}], ["json", {"outputFile": "test-results/test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 4, "webServer": null}, "suites": [{"title": "admin-functionality-audit.spec.js", "file": "admin-functionality-audit.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "后台管理中心功能审查", "file": "admin-functionality-audit.spec.js", "line": 25, "column": 6, "specs": [{"title": "1. 访问静态管理页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2455, "errors": [], "stdout": [{"text": "🔍 测试静态管理页面访问...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:21:32.775Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "667c7262401491d2deca-b8abd2d815cd6173b6c3", "file": "admin-functionality-audit.spec.js", "line": 57, "column": 3}, {"title": "2. 检查侧边栏菜单功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 31498, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🔍 测试侧边栏菜单功能...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 0/15\n"}, {"text": "❌ 失败测试: 10/15\n"}, {"text": "🔍 发现功能缺失: 1项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:21:39.710Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-2-检查侧边栏菜单功能-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-2-检查侧边栏菜单功能-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-2-检查侧边栏菜单功能-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "667c7262401491d2deca-5b5f4bddd3521ffbaa85", "file": "admin-functionality-audit.spec.js", "line": 89, "column": 3}, {"title": "3. 检查仪表板数据展示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 1710, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:150:23", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 23, "line": 150}, "snippet": "\u001b[0m \u001b[90m 148 |\u001b[39m     \u001b[90m// 检查统计卡片\u001b[39m\n \u001b[90m 149 |\u001b[39m     \u001b[36mconst\u001b[39m statCards \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 150 |\u001b[39m     expect(statCards)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 151 |\u001b[39m     \n \u001b[90m 152 |\u001b[39m     \u001b[90m// 检查数据是否为模拟数据\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mconst\u001b[39m pendingCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-number'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 23, "line": 150}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 148 |\u001b[39m     \u001b[90m// 检查统计卡片\u001b[39m\n \u001b[90m 149 |\u001b[39m     \u001b[36mconst\u001b[39m statCards \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 150 |\u001b[39m     expect(statCards)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 151 |\u001b[39m     \n \u001b[90m 152 |\u001b[39m     \u001b[90m// 检查数据是否为模拟数据\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mconst\u001b[39m pendingCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-number'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:150:23\u001b[22m"}], "stdout": [{"text": "🔍 测试仪表板数据展示...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 0/3\n"}, {"text": "❌ 失败测试: 0/3\n"}, {"text": "🔍 发现功能缺失: 0项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:12.167Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-3-检查仪表板数据展示-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-3-检查仪表板数据展示-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-3-检查仪表板数据展示-chromium/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 23, "line": 150}}], "status": "unexpected"}], "id": "667c7262401491d2deca-07cf8349d46a8ea26353", "file": "admin-functionality-audit.spec.js", "line": 142, "column": 3}, {"title": "4. 检查表格交互功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 11998, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('.el-table')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('.el-table')\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('.el-table')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('.el-table')\u001b[22m\n\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:173:25", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 25, "line": 173}, "snippet": "\u001b[0m \u001b[90m 171 |\u001b[39m     \u001b[90m// 检查最近申请表格\u001b[39m\n \u001b[90m 172 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.el-table'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 173 |\u001b[39m     \u001b[36mawait\u001b[39m expect(table)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 174 |\u001b[39m     \n \u001b[90m 175 |\u001b[39m     \u001b[90m// 检查操作按钮\u001b[39m\n \u001b[90m 176 |\u001b[39m     \u001b[36mconst\u001b[39m approveButtons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"通过\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 25, "line": 173}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('.el-table')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('.el-table')\u001b[22m\n\n\n\u001b[0m \u001b[90m 171 |\u001b[39m     \u001b[90m// 检查最近申请表格\u001b[39m\n \u001b[90m 172 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.el-table'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 173 |\u001b[39m     \u001b[36mawait\u001b[39m expect(table)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 174 |\u001b[39m     \n \u001b[90m 175 |\u001b[39m     \u001b[90m// 检查操作按钮\u001b[39m\n \u001b[90m 176 |\u001b[39m     \u001b[36mconst\u001b[39m approveButtons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"通过\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:173:25\u001b[22m"}], "stdout": [{"text": "🔍 测试表格交互功能...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 0/3\n"}, {"text": "❌ 失败测试: 0/3\n"}, {"text": "🔍 发现功能缺失: 0项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:15.542Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-4-检查表格交互功能-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-4-检查表格交互功能-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-4-检查表格交互功能-chromium/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 25, "line": 173}}], "status": "unexpected"}], "id": "667c7262401491d2deca-614ec0da23bf18ea6d80", "file": "admin-functionality-audit.spec.js", "line": 165, "column": 3}, {"title": "5. 检查用户下拉菜单", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 1548, "errors": [], "stdout": [{"text": "🔍 测试用户下拉菜单...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:29.190Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "667c7262401491d2deca-7222621b933da67e2dc9", "file": "admin-functionality-audit.spec.js", "line": 206, "column": 3}, {"title": "6. 检查响应式设计", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 3392, "errors": [], "stdout": [{"text": "🔍 测试响应式设计...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 4/9\n"}, {"text": "❌ 失败测试: 0/9\n"}, {"text": "🔍 发现功能缺失: 1项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:31.769Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "667c7262401491d2deca-21f55be9f2cd7c7deb1a", "file": "admin-functionality-audit.spec.js", "line": 244, "column": 3}, {"title": "1. 访问静态管理页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 1829, "errors": [], "stdout": [{"text": "🔍 测试静态管理页面访问...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:21:32.778Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "667c7262401491d2deca-0fa7bf6a88059736955e", "file": "admin-functionality-audit.spec.js", "line": 57, "column": 3}, {"title": "2. 检查侧边栏菜单功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 31047, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🔍 测试侧边栏菜单功能...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 0/15\n"}, {"text": "❌ 失败测试: 10/15\n"}, {"text": "🔍 发现功能缺失: 1项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:21:39.049Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-2-检查侧边栏菜单功能-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-2-检查侧边栏菜单功能-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-2-检查侧边栏菜单功能-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "667c7262401491d2deca-0cc9003fd7daf7057ff5", "file": "admin-functionality-audit.spec.js", "line": 89, "column": 3}, {"title": "3. 检查仪表板数据展示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 2, "parallelIndex": 1, "status": "failed", "duration": 1742, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:150:23", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 23, "line": 150}, "snippet": "\u001b[0m \u001b[90m 148 |\u001b[39m     \u001b[90m// 检查统计卡片\u001b[39m\n \u001b[90m 149 |\u001b[39m     \u001b[36mconst\u001b[39m statCards \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 150 |\u001b[39m     expect(statCards)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 151 |\u001b[39m     \n \u001b[90m 152 |\u001b[39m     \u001b[90m// 检查数据是否为模拟数据\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mconst\u001b[39m pendingCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-number'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 23, "line": 150}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 148 |\u001b[39m     \u001b[90m// 检查统计卡片\u001b[39m\n \u001b[90m 149 |\u001b[39m     \u001b[36mconst\u001b[39m statCards \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mcount()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 150 |\u001b[39m     expect(statCards)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 151 |\u001b[39m     \n \u001b[90m 152 |\u001b[39m     \u001b[90m// 检查数据是否为模拟数据\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mconst\u001b[39m pendingCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-card'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mlocator(\u001b[32m'.stat-number'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:150:23\u001b[22m"}], "stdout": [{"text": "🔍 测试仪表板数据展示...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 0/3\n"}, {"text": "❌ 失败测试: 0/3\n"}, {"text": "🔍 发现功能缺失: 0项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:11.853Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-3-检查仪表板数据展示-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-3-检查仪表板数据展示-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-3-检查仪表板数据展示-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 23, "line": 150}}], "status": "unexpected"}], "id": "667c7262401491d2deca-2289da36f963ba3969a8", "file": "admin-functionality-audit.spec.js", "line": 142, "column": 3}, {"title": "4. 检查表格交互功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 5, "parallelIndex": 1, "status": "failed", "duration": 12014, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('.el-table')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('.el-table')\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('.el-table')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('.el-table')\u001b[22m\n\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:173:25", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 25, "line": 173}, "snippet": "\u001b[0m \u001b[90m 171 |\u001b[39m     \u001b[90m// 检查最近申请表格\u001b[39m\n \u001b[90m 172 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.el-table'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 173 |\u001b[39m     \u001b[36mawait\u001b[39m expect(table)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 174 |\u001b[39m     \n \u001b[90m 175 |\u001b[39m     \u001b[90m// 检查操作按钮\u001b[39m\n \u001b[90m 176 |\u001b[39m     \u001b[36mconst\u001b[39m approveButtons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"通过\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 25, "line": 173}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m failed\n\nLocator:  locator('.el-table')\nExpected: visible\nReceived: <element(s) not found>\nTimeout:  10000ms\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('.el-table')\u001b[22m\n\n\n\u001b[0m \u001b[90m 171 |\u001b[39m     \u001b[90m// 检查最近申请表格\u001b[39m\n \u001b[90m 172 |\u001b[39m     \u001b[36mconst\u001b[39m table \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.el-table'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 173 |\u001b[39m     \u001b[36mawait\u001b[39m expect(table)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 174 |\u001b[39m     \n \u001b[90m 175 |\u001b[39m     \u001b[90m// 检查操作按钮\u001b[39m\n \u001b[90m 176 |\u001b[39m     \u001b[36mconst\u001b[39m approveButtons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"通过\")'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js:173:25\u001b[22m"}], "stdout": [{"text": "🔍 测试表格交互功能...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 0/3\n"}, {"text": "❌ 失败测试: 0/3\n"}, {"text": "🔍 发现功能缺失: 0项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:15.545Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-4-检查表格交互功能-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-4-检查表格交互功能-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/admin-functionality-audit-后台管理中心功能审查-4-检查表格交互功能-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/admin-functionality-audit.spec.js", "column": 25, "line": 173}}], "status": "unexpected"}], "id": "667c7262401491d2deca-c1af44a27c739e9bb3b2", "file": "admin-functionality-audit.spec.js", "line": 165, "column": 3}, {"title": "5. 检查用户下拉菜单", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "passed", "duration": 1594, "errors": [], "stdout": [{"text": "🔍 测试用户下拉菜单...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:29.189Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "667c7262401491d2deca-08dc926e165033a29a94", "file": "admin-functionality-audit.spec.js", "line": 206, "column": 3}, {"title": "6. 检查响应式设计", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "passed", "duration": 3287, "errors": [], "stdout": [{"text": "🔍 测试响应式设计...\n"}, {"text": "Network Error: http://localhost:4001/admin/dashboard.html \u001b[33m404\u001b[39m\n"}, {"text": "Console Error: Failed to load resource: the server responded with a status of 404 (Not Found)\n"}, {"text": "\n📊 生成功能审查报告...\n"}, {"text": "📋 审查报告已保存到: test-results/admin-functionality-audit-report.json\n"}, {"text": "✅ 通过测试: 4/9\n"}, {"text": "❌ 失败测试: 0/9\n"}, {"text": "🔍 发现功能缺失: 1项\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T16:22:31.816Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "667c7262401491d2deca-3687c34b71a596512bf8", "file": "admin-functionality-audit.spec.js", "line": 244, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-25T16:21:32.214Z", "duration": 63339.473, "expected": 6, "skipped": 0, "unexpected": 6, "flaky": 0}}