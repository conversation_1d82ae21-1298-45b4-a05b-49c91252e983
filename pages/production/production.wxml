<!-- pages/production/production.wxml -->
<view class="production-container">
  <!-- Tab导航 -->
  <c-tab-bar 
    tabs="{{tabs}}" 
    current="{{activeTab}}"
    bind:change="onTabChange"
  ></c-tab-bar>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 生产管理页面 -->
    <view wx:if="{{activeTab === 0}}" class="production-section">
      <view class="section-header">
        <text class="section-title">生产管理</text>
      </view>

      <!-- 添加生产记录按钮 -->
      <view class="add-record-section">
        <button class="add-record-btn" bindtap="onAddProductionRecord">
          <image class="btn-icon" src="/images/icons/plus.png" mode="aspectFit"></image>
          <text>添加生产记录</text>
        </button>
      </view>

      <!-- 记录类型筛选 -->
      <scroll-view class="record-types" scroll-x>
        <view class="type-list">
          <view class="type-item {{activeRecordTab === 'all' ? 'active' : ''}}" data-tab="all" bindtap="onRecordTabChange">全部记录</view>
          <view class="type-item {{activeRecordTab === 'entry' ? 'active' : ''}}" data-tab="entry" bindtap="onRecordTabChange">入栏记录</view>
          <view class="type-item {{activeRecordTab === 'weight' ? 'active' : ''}}" data-tab="weight" bindtap="onRecordTabChange">称重记录</view>
          <view class="type-item {{activeRecordTab === 'sale' ? 'active' : ''}}" data-tab="sale" bindtap="onRecordTabChange">出栏记录</view>
        </view>
      </scroll-view>

      <!-- 生产记录列表 -->
      <view class="production-records">
        <!-- 空状态 -->
        <view wx:if="{{filteredProductionRecords.length === 0}}" class="empty-state">
          <view class="empty-icon">📝</view>
          <text class="empty-text">暂无{{activeRecordTab === 'all' ? '' : activeRecordTab === 'entry' ? '入栏' : activeRecordTab === 'weight' ? '称重' : '出栏'}}记录</text>
          <text class="empty-tip">点击上方按钮添加生产记录</text>
        </view>
        
        <block wx:for="{{filteredProductionRecords}}" wx:key="id">
          <view class="record-card" bindtap="onViewProductionRecord" data-id="{{item.id}}">
            <!-- 简化的卡片头部，不显示记录类型和日期 -->
            
            <!-- 批次信息条 -->
            <view class="batch-bar">
              <view class="batch-info">
                <text class="batch-label">批次</text>
                <text class="batch-number batch-color-{{item.batchColorIndex || 0}}">{{item.batch}}</text>
              </view>
              <view wx:if="{{item.status}}" class="batch-status status-{{item.status}}">
                <text class="status-text">{{item.statusText || '进行中'}}</text>
              </view>
            </view>
            
            <!-- 主要数据展示 -->
            <view class="data-section">
              <!-- 入栏记录 -->
              <view wx:if="{{item.type === 'entry'}}" class="entry-data">
                <view class="primary-metrics">
                  <view class="metric-item">
                    <text class="metric-value">{{item.details.count}}</text>
                    <text class="metric-label">只</text>
                  </view>
                  <view class="metric-divider"></view>
                  <view class="metric-item">
                    <text class="metric-value cost">¥{{item.details.cost}}</text>
                    <text class="metric-label">总成本</text>
                  </view>
                </view>
                <!-- 移除牧场来源和鹅品种信息 -->
              </view>
              
              <!-- 称重记录 -->
              <view wx:elif="{{item.type === 'weight'}}" class="weight-data">
                <view class="primary-metrics">
                  <view class="metric-item">
                    <text class="metric-value">{{item.details.count}}</text>
                    <text class="metric-label">只</text>
                  </view>
                  <view class="metric-divider"></view>
                  <view class="metric-item">
                    <text class="metric-value weight">{{item.details.weight}}</text>
                    <text class="metric-label">kg/只</text>
                  </view>
                </view>
                <!-- 生长进度已移至底部 -->
              </view>
              
              <!-- 出栏记录 -->
              <view wx:else class="sale-data">
                <view class="primary-metrics">
                  <view class="metric-item">
                    <text class="metric-value">{{item.details.count}}</text>
                    <text class="metric-label">只</text>
                  </view>
                  <view class="metric-divider"></view>
                  <view class="metric-item">
                    <text class="metric-value income">¥{{item.details.totalIncome}}</text>
                    <text class="metric-label">总收入</text>
                  </view>
                </view>
                <!-- 次要信息已移至底部 -->
              </view>
            </view>
            
            <!-- 卡片底部：记录类型+日期 + 查看详情 -->
            <view class="card-footer">
              <view class="footer-left">
                <view class="record-type-badge type-{{item.type}}">
                  <text class="type-icon">{{item.type === 'entry' ? '📥' : item.type === 'weight' ? '⚖️' : '📤'}}</text>
                  <text class="type-text">{{item.type === 'entry' ? '入栏记录' : item.type === 'weight' ? '称重记录' : '出栏记录'}}</text>
                </view>
                <text class="date-text">{{item.date}}</text>
              </view>
              <view class="footer-right">
                <text class="view-detail">查看详情</text>
                <text class="arrow-icon">→</text>
              </view>
            </view>
          </view>
        </block>
      </view>

      <!-- 底部间距占位 -->
      <view class="bottom-spacing"></view>
    </view>

    <!-- 健康管理页面 -->
    <view wx:elif="{{activeTab === 1}}" class="records-section">
      <!-- 二级标签页导航 -->
      <scroll-view class="health-sub-tabs" scroll-x>
        <view class="sub-tab-list">
          <block wx:for="{{healthTabs}}" wx:key="id">
            <view class="sub-tab-item {{activeHealthTab === item.id ? 'active' : ''}}" 
                  data-tab="{{item.id}}" 
                  bindtap="onHealthTabChange">
              {{item.name}}
            </view>
          </block>
        </view>
      </scroll-view>

      <!-- 健康记录子页面 -->
      <view wx:if="{{activeHealthTab === 0}}" class="health-records-content">
        <view class="records-list">
          <block wx:for="{{healthRecords}}" wx:key="id">
            <view class="record-item" bindtap="onViewRecord" data-id="{{item.id}}">
              <view class="record-header">
                <text class="record-title">{{item.title}}</text>
                <text class="record-status status-{{item.status === '防疫记录' ? 'vaccination' : item.status === '生病记录' ? 'sick' : item.status === '治疗记录' ? 'treatment' : item.status === '死亡记录' ? 'death' : 'healthy'}}">{{item.status}}</text>
              </view>
              <view class="record-date">{{item.date}}</view>
              <view class="record-desc">{{item.description}}</view>
            </view>
          </block>

          <c-empty-state wx:if="{{healthRecords.length === 0 && !loading}}" title="暂无健康记录"></c-empty-state>
          
          <!-- 添加记录按钮 - 显示在记录列表下方 -->
          <view class="add-record-btn" bindtap="onAddRecord">
            <image class="add-icon" src="/images/icons/add.png" mode="aspectFit"></image>
            <text class="add-record-text">添加健康记录</text>
          </view>
        </view>
      </view>

      <!-- 健康报告子页面 -->
      <view wx:elif="{{activeHealthTab === 1}}" class="health-report-content">
        <!-- 报告类型筛选 -->
        <scroll-view class="report-types" scroll-x>
          <view class="type-list">
            <block wx:for="{{reportTypes}}" wx:key="id">
              <view class="type-item {{activeReportType === item.id ? 'active' : ''}}" data-type="{{item.id}}" bindtap="onReportTypeChange">
                {{item.name}}
              </view>
            </block>
          </view>
        </scroll-view>

        <!-- 健康概览 -->
        <view class="overview-card">
          <view class="section-header">
            <text class="section-title">健康概览</text>
          </view>
          
          <!-- 四宫格统计卡片 -->
          <view class="overview-stats">
            <view class="stat-item total">
              <text class="stat-value">{{reportData.overview.totalGeese}}</text>
              <text class="stat-label">总数量</text>
            </view>
            
            <view class="stat-item healthy">
              <text class="stat-value">{{reportData.overview.healthyCount}}</text>
              <text class="stat-label">健康</text>
            </view>
            
            <view class="stat-item sick">
              <text class="stat-value">{{reportData.overview.sickCount}}</text>
              <text class="stat-label">患病</text>
            </view>
            
            <view class="stat-item death">
              <text class="stat-value">{{reportData.overview.deathCount}}</text>
              <text class="stat-label">死亡</text>
            </view>
          </view>
          
          <!-- 健康率显示 -->
          <view class="health-rate">
            <text class="rate-label">健康率</text>
            <text class="rate-value">{{reportData.overview.healthyRate}}</text>
          </view>
        </view>

        <!-- 疾病统计 -->
        <view class="disease-card">
          <view class="section-header">
            <text class="section-title">疾病统计</text>
          </view>
          <view class="disease-stats">
            <block wx:for="{{reportData.diseaseStats}}" wx:key="name">
              <view class="disease-item">
                <text class="disease-name">{{item.name}}</text>
                <text class="disease-value">{{item.value}}例</text>
                <text class="disease-rate">{{item.rate}}</text>
              </view>
            </block>
          </view>
        </view>

        <!-- 治疗效果 -->
        <view class="treatment-card">
          <view class="section-header">
            <text class="section-title">治疗效果</text>
          </view>
          <view class="treatment-stats">
            <block wx:for="{{reportData.treatmentStats}}" wx:key="name">
              <view class="treatment-item">
                <text class="treatment-name">{{item.name}}</text>
                <text class="treatment-value">{{item.value}}例</text>
                <text class="treatment-rate">{{item.rate}}</text>
              </view>
            </block>
          </view>
        </view>
      </view>

      <!-- AI诊断子页面 -->
      <view wx:elif="{{activeHealthTab === 2}}" class="ai-diagnosis-content">
        <view class="diagnosis-container">
          <view class="card">
            <view class="card-title">症状描述</view>
            <textarea class="symptom-input" placeholder="请详细描述鹅群的症状表现..." value="{{symptoms}}" bindinput="onSymptomInput"></textarea>
          </view>

          <view class="card">
            <view class="card-title">图片上传</view>
            <view class="image-upload">
              <block wx:for="{{uploadedImages}}" wx:key="index">
                <view class="uploaded-image">
                  <image src="{{item.url}}" mode="aspectFill" bindtap="onDeleteImage" data-index="{{index}}"></image>
                  <view class="delete-btn" bindtap="onDeleteImage" data-index="{{index}}">✕</view>
                </view>
              </block>

              <view wx:if="{{uploadedImages.length < 3}}" class="upload-btn" bindtap="onChooseImage">
                <image src="/images/icon_camera.png" mode="aspectFit"></image>
                <text>上传图片</text>
              </view>
            </view>
            <view class="upload-tip">最多可上传3张图片，支持拍照或从相册选择</view>
          </view>

          <button class="btn btn-primary btn-block" bindtap="onStartDiagnosis" loading="{{isDiagnosing}}">
            {{isDiagnosing ? '诊断中...' : '开始AI诊断'}}
          </button>

          <!-- 诊断结果 -->
          <view wx:if="{{diagnosisResult}}" class="diagnosis-result">
            <view class="result-header">
              <text class="result-title">诊断结果</text>
              <button class="clear-btn" bindtap="onClearResult">重新诊断</button>
            </view>

            <view class="result-content">
              <view class="disease-info">
                <text class="disease-name">{{diagnosisResult.disease}}</text>
                <text class="confidence">置信度: {{diagnosisResult.confidence}}</text>
              </view>

              <view class="description">{{diagnosisResult.description}}</view>

              <view class="suggestions">
                <text class="section-title">治疗建议</text>
                <block wx:for="{{diagnosisResult.suggestions}}" wx:key="index">
                  <view class="suggestion-item">{{index + 1}}. {{item}}</view>
                </block>
              </view>

              <view class="medications">
                <text class="section-title">推荐用药</text>
                <block wx:for="{{diagnosisResult.medications}}" wx:key="name">
                  <view class="medication-item">
                    <text class="med-name">{{item.name}}</text>
                    <text class="med-dosage">{{item.dosage}}</text>
                  </view>
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 物料管理页面 -->
    <view wx:elif="{{activeTab === 2}}" class="materials-section">
      <view class="section-header">
        <text class="section-title">物料管理</text>
      </view>

      <!-- 物料统计 -->
      <view class="material-stats">
        <view class="stats-item" bindtap="onMaterialStatsTap" data-type="feed">
          <text class="stats-value">{{materialStats.feedCount || 3}}</text>
          <text class="stats-label">饲料种类</text>
        </view>
        <view class="stats-item" bindtap="onMaterialStatsTap" data-type="medicine">
          <text class="stats-value">{{materialStats.medicineCount || 2}}</text>
          <text class="stats-label">药品种类</text>
        </view>
        <view class="stats-item warning" bindtap="onMaterialStatsTap" data-type="lowstock">
          <text class="stats-value">{{materialStats.lowStockItems || 2}}</text>
          <text class="stats-label">库存不足</text>
        </view>
      </view>

      <!-- 快捷操作 - 添加物料记录 -->
      <view class="quick-actions">
        <button class="add-material-btn primary" bindtap="onAddMaterial">
          <image class="btn-icon" src="/images/icons/plus.png" mode="aspectFit"></image>
          <text>添加物料记录</text>
        </button>
      </view>

      <!-- 物料分类Tab -->
      <scroll-view class="material-tabs" scroll-x>
        <view class="tab-list">
          <block wx:for="{{materialTabs}}" wx:key="id">
            <view class="tab-item {{activeMaterialTab === item.id ? 'active' : ''}}"
                  data-tab="{{item.id}}"
                  bindtap="onMaterialTabChange">
              {{item.name}}
            </view>
          </block>
        </view>
      </scroll-view>

      <!-- 物料列表 -->
      <view class="material-list">
        <block wx:for="{{filteredMaterialList}}" wx:key="id">
          <view class="material-item">
            <view class="material-info">
              <text class="material-name">{{item.name}}</text>
              <text class="material-spec">{{item.spec}}</text>
            </view>
            <view class="material-stock">
              <text class="stock-count">库存: {{item.stock}}{{item.category === 'medicine' ? '瓶' : item.category === 'other' ? '个' : '袋'}}</text>
              <text class="stock-status {{item.status}}">
                {{item.status === 'normal' ? '充足' : item.status === 'warning' ? '偏低' : '不足'}}
              </text>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <view wx:if="{{filteredMaterialList.length === 0}}" class="empty-state">
          <text class="empty-text">暂无{{activeMaterialTab === 'all' ? '' : activeMaterialTab === 'feed' ? '饲料' : activeMaterialTab === 'medicine' ? '药品' : '其他'}}物料</text>
        </view>
      </view>

      <!-- 底部间距占位 -->
      <view class="bottom-spacing"></view>
    </view>

    <!-- 知识库页面 -->
    <view wx:elif="{{activeTab === 3}}" class="ai-intelligent-section">
      <!-- 知识库部分 -->
      <view class="knowledge-section">
        <view class="section-header">
          <text class="section-title">知识库</text>
        </view>
        
        <!-- 搜索栏 -->
        <view class="search-bar">
          <view class="search-input-container">
            <image class="search-icon" src="/images/icon_search.png" mode="aspectFit"></image>
            <input class="search-input" placeholder="搜索知识文章" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
          </view>
          <button class="search-btn" bindtap="onSearch">搜索</button>
        </view>

        <!-- 分类筛选 -->
        <scroll-view class="category-scroll" scroll-x>
          <view class="category-list">
            <block wx:for="{{categories}}" wx:key="id">
              <view class="category-item {{activeCategory === item.id ? 'active' : ''}}" data-category="{{item.id}}" bindtap="onCategoryChange">
                {{item.name}}
              </view>
            </block>
          </view>
        </scroll-view>

        <!-- 文章列表 -->
        <view class="articles-container">
          <block wx:for="{{articles}}" wx:key="id">
            <view class="article-item" bindtap="onViewArticle" data-id="{{item.id}}">
              <view class="article-title">{{item.title}}</view>
              <view class="article-meta">
                <view class="article-category">{{item.categoryName}}</view>
                <view class="article-date">{{item.publishDate}}</view>
                <view class="article-read-count">阅读 {{item.readCount}}</view>
              </view>
              <view class="article-summary">{{item.summary}}</view>
            </view>
          </block>

          <!-- 空状态 -->
          <view wx:if="{{articles.length === 0 && !loading}}" class="empty-state">
            <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
            <text class="empty-text">暂无相关文章</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 生产记录详情弹窗 -->
  <c-record-detail-modal
    visible="{{showRecordDetail}}"
    recordData="{{currentRecord}}"
    showEdit="{{false}}"
    bind:close="onCloseRecordDetail"
    bind:confirm="onCloseRecordDetail"
  ></c-record-detail-modal>

</view>