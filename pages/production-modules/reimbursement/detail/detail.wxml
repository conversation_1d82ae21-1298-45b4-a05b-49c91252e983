<!-- pages/production/reimbursement/detail/detail.wxml -->
<view class="detail-container">
  <!-- 报销信息 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">报销信息</text>
      <view class="status-badge {{reimbursement.status}}">{{reimbursement.statusText}}</view>
    </view>
    <view class="info-list">
      <view class="info-item">
        <text class="info-label">报销标题</text>
        <text class="info-value">{{reimbursement.title}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">报销类型</text>
        <text class="info-value">{{reimbursement.category}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">报销金额</text>
        <text class="info-value amount">¥{{reimbursement.amount}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">申请人</text>
        <text class="info-value">{{reimbursement.applicant}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">申请时间</text>
        <text class="info-value">{{reimbursement.createTime}}</text>
      </view>
      <view wx:if="{{reimbursement.description}}" class="info-item">
        <text class="info-label">报销说明</text>
        <text class="info-value">{{reimbursement.description}}</text>
      </view>
    </view>
  </view>

  <!-- 报销凭证 -->
  <view wx:if="{{reimbursement.images && reimbursement.images.length > 0}}" class="section">
    <view class="section-header">
      <text class="section-title">报销凭证</text>
    </view>
    <view class="images-grid">
      <block wx:for="{{reimbursement.images}}" wx:key="index">
        <image class="evidence-image" src="{{item}}" mode="aspectFill" bindtap="onPreviewImage" data-url="{{item}}"></image>
      </block>
    </view>
  </view>

  <!-- 审批记录 -->
  <view wx:if="{{approvalRecords.length > 0}}" class="section">
    <view class="section-header">
      <text class="section-title">审批记录</text>
    </view>
    <view class="approval-list">
      <block wx:for="{{approvalRecords}}" wx:key="id">
        <view class="approval-item">
          <view class="approval-info">
            <text class="approval-person">{{item.approver}}</text>
            <text class="approval-time">{{item.time}}</text>
          </view>
          <view class="approval-status {{item.status}}">{{item.statusText}}</view>
          <view wx:if="{{item.remark}}" class="approval-remark">{{item.remark}}</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view wx:if="{{showActions}}" class="action-section">
    <button wx:if="{{canApprove}}" class="action-btn approve" bindtap="onApprove">通过</button>
    <button wx:if="{{canReject}}" class="action-btn reject" bindtap="onReject">拒绝</button>
    <button wx:if="{{canEdit}}" class="action-btn edit" bindtap="onEdit">编辑</button>
    <button wx:if="{{canCancel}}" class="action-btn cancel" bindtap="onCancel">撤销</button>
  </view>
</view>
