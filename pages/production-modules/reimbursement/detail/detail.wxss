/* pages/production/reimbursement/detail/detail.wxss */
.detail-container {
  padding: 20rpx;
  background-color: #F5F6F8;
  min-height: 100vh;
}

.section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.status-badge.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-badge.approved {
  background: #f6ffed;
  color: #52c41a;
}

.status-badge.rejected {
  background: #fff2f0;
  color: #ff4d4f;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.info-value.amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #0066CC;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.evidence-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

.approval-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.approval-item {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #0066CC;
}

.approval-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.approval-person {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.approval-time {
  font-size: 24rpx;
  color: #999;
}

.approval-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  display: inline-block;
  margin-bottom: 12rpx;
}

.approval-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.approval-status.approved {
  background: #f6ffed;
  color: #52c41a;
}

.approval-status.rejected {
  background: #fff2f0;
  color: #ff4d4f;
}

.approval-remark {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.action-section {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: white;
  border-radius: 20rpx;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx 0;
  border-radius: var(--radius-xl);
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.action-btn.approve {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
}

.action-btn.reject {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: white;
}

.action-btn.edit {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
}

.action-btn.cancel {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #d9d9d9;
}