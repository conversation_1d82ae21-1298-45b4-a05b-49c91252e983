// pages/production/reimbursement/add/add.js
Page({
  data: {
    formData: {
      title: '',
      categoryIndex: 0,
      amount: '',
      description: '',
      images: []
    },
    categoryOptions: ['差旅费', '办公用品', '通讯费', '培训费', '其他费用'],
    canSubmit: false
  },

  onLoad: function (options) {
    // 页面加载
  },

  // 表单输入处理
  onTitleInput: function(e) {
    this.setData({
      'formData.title': e.detail.value
    });
    this.checkCanSubmit();
  },

  onCategoryChange: function(e) {
    this.setData({
      'formData.categoryIndex': e.detail.value
    });
  },

  onAmountInput: function(e) {
    this.setData({
      'formData.amount': e.detail.value
    });
    this.checkCanSubmit();
  },

  onDescriptionInput: function(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  // 检查是否可以提交
  checkCanSubmit: function() {
    const { title, amount } = this.data.formData;
    const canSubmit = title.trim() !== '' && amount.trim() !== '' && parseFloat(amount) > 0;
    this.setData({
      canSubmit: canSubmit
    });
  },

  // 选择图片
  onChooseImage: function() {
    const that = this;
    wx.chooseImage({
      count: 3 - this.data.formData.images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        const newImages = that.data.formData.images.concat(res.tempFilePaths);
        that.setData({
          'formData.images': newImages
        });
      }
    });
  },

  // 预览图片
  onPreviewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.formData.images
    });
  },

  // 删除图片
  onDeleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.formData.images;
    images.splice(index, 1);
    this.setData({
      'formData.images': images
    });
  },

  // 提交表单
  onSubmit: function(e) {
    if (!this.data.canSubmit) {
      return;
    }

    const formData = this.data.formData;
    const submitData = {
      title: formData.title,
      category: this.data.categoryOptions[formData.categoryIndex],
      amount: parseFloat(formData.amount),
      description: formData.description,
      images: formData.images
    };

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  }
});
