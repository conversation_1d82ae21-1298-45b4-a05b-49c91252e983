// pages/production/record-add/record-add.js
const { API, UI, BUSINESS } = require('../../../constants/index.js');
const request = require('../../../utils/request.js');
const { FinanceService } = require('../../../utils/business/finance-service.js');
const { BatchManagement } = require('../../../utils/business/batch-management.js');

Page({
  data: {
    // 标签页管理
    activeTab: 0,
    tabs: [
      { id: 0, name: '入栏记录', type: 'entry' },
      { id: 1, name: '称重记录', type: 'weight' },
      { id: 2, name: '出栏记录', type: 'sale' }
    ],
    recordType: 'entry', // entry, weight, sale
    
    // 通用字段
    batch: '',
    date: '',
    count: '',
    weight: '',
    notes: '',
    
    // 入栏记录特有字段
    source: '',
    breed: '',
    age: '',
    cost: '',
    supplier: '',
    
    // 出栏记录特有字段
    price: '',
    buyer: '',
    totalIncome: '',
    
    // 选项数据
    breedOptions: [
      { label: '太湖鹅', value: 'taihu' },
      { label: '四川白鹅', value: 'sichuan' },
      { label: '皖西白鹅', value: 'wanxi' },
      { label: '扬州鹅', value: 'yangzhou' },
      { label: '其他品种', value: 'other' }
    ],
    batchOptions: [], // 批次选择选项
    activeBatches: [], // 活跃批次数据
    
    // 批次相关信息
    batchLabel: '',
    selectedBatchInfo: null,
    maxCount: 0, // 批次可操作的最大数量
    entryDate: '', // 入栏日期
    
    loading: false
  },

  onLoad: function (options) {
    // 获取记录类型，默认为入栏记录
    const recordType = options.type || 'entry';
    
    // 生成默认日期
    const now = new Date();
    const today = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    
    this.setData({
      recordType: recordType,
      date: today
    });
    
    // 根据记录类型初始化数据
    this.initializeByRecordType(recordType);
  },
  
  // 根据记录类型初始化数据
  initializeByRecordType: function(recordType) {
    if (recordType === 'entry') {
      // 入栏记录：生成新批次号
      this.generateNewBatchNumber();
    } else {
      // 称重和出栏记录：加载已有批次选项
      this.loadActiveBatches();
    }
  },
  
  // 生成新的批次号（仅用于入栏记录）
  generateNewBatchNumber: function() {
    const now = new Date();
    const baseNumber = `QY-${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
    
    // 检查当前日期是否已有批次，如果有则加序号
    this.checkBatchNumberUniqueness(baseNumber, 1);
  },
  
  // 检查批次号唯一性
  checkBatchNumberUniqueness: function(baseNumber, sequence) {
    const batchNumber = sequence === 1 ? baseNumber : `${baseNumber}-${sequence}`;
    
    // 这里应该调用API检查批次号是否已存在
    // 暂时使用模拟逻辑
    const existingBatches = wx.getStorageSync('existing_batches') || [];
    
    if (existingBatches.includes(batchNumber)) {
      // 如果已存在，递增序号
      this.checkBatchNumberUniqueness(baseNumber, sequence + 1);
    } else {
      // 如果不存在，使用此批次号
      this.setData({
        batch: batchNumber
      });
    }
  },
  
  // 加载活跃批次（用于称重和出栏记录）
  loadActiveBatches: async function() {
    // 显示加载提示
    wx.showLoading({
      title: '加载批次数据...',
      mask: true
    });
    
    try {
      // 从批次管理服务获取活跃批次数据
      const activeBatches = await BatchManagement.getActiveBatches();
      
      // 格式化为选择器选项
      const batchOptions = activeBatches.map(batch => ({
        value: batch.batchNumber,
        label: `${batch.batchNumber} (${batch.breed}, ${batch.currentCount}只)`,
        batchNumber: batch.batchNumber,
        breed: batch.breed,
        currentCount: batch.currentCount,
        entryDate: batch.entryDate,
        avgWeight: batch.averageWeight > 0 ? `${batch.averageWeight}kg` : '未称重',
        status: batch.status,
        statusLabel: BatchManagement.getStatusLabel(batch.status)
      }));
      
      this.setData({
        batchOptions: batchOptions,
        activeBatches: activeBatches
      });
      
      // 隐藏加载提示
      wx.hideLoading();
      
      // 如果没有活跃批次，给出提示
      if (activeBatches.length === 0) {
        wx.showToast({
          title: '暂无活跃批次，请先添加入栏记录',
          icon: 'none',
          duration: 3000
        });
      }
      
    } catch (error) {
      // 隐藏加载提示
      wx.hideLoading();
      
      console.error('加载活跃批次失败:', error);
      wx.showToast({
        title: '加载批次数据失败',
        icon: 'none'
      });
      
      // 设置空数据
      this.setData({
        batchOptions: [],
        activeBatches: []
      });
    }
  },

  // 标签页切换
  onTabChange: function(e) {
    const tabId = e.currentTarget.dataset.tab;
    const selectedTab = this.data.tabs.find(tab => tab.id === tabId);
    
    this.setData({
      activeTab: tabId,
      recordType: selectedTab.type
    });
    
    // 清空表单数据，保留通用字段
    this.resetFormData();
    
    // 根据新的记录类型重新初始化数据
    this.initializeByRecordType(selectedTab.type);
  },

  // 重置表单数据
  resetFormData: function() {
    // 保留批次号和日期，清空其他字段
    const { batch, date } = this.data;
    this.setData({
      count: '',
      weight: '',
      notes: '',
      source: '',
      breed: '',
      age: '',
      cost: '',
      supplier: '',
      price: '',
      buyer: '',
      totalIncome: ''
    });
  },

  // 表单输入处理
  onBatchInput: function(e) {
    this.setData({ batch: e.detail.value });
  },

  onDateChange: function(e) {
    this.setData({ date: e.detail.value });
  },

  onCountInput: function(e) {
    const inputCount = parseInt(e.detail.value) || 0;
    const maxCount = this.data.maxCount || 0;
    
    // 验证数量不能超过最大可操作数量（称重和出栏记录）
    if (this.data.recordType !== 'entry' && maxCount > 0 && inputCount > maxCount) {
      wx.showToast({
        title: `数量不能超过${maxCount}只`,
        icon: 'none'
      });
      
      this.setData({ 
        count: maxCount.toString()
      });
      this.calculateTotalIncome();
      return;
    }
    
    this.setData({ count: inputCount });
    this.calculateTotalIncome();
  },

  onWeightInput: function(e) {
    const weight = parseFloat(e.detail.value) || 0;
    this.setData({ weight: weight });
  },

  onSourceInput: function(e) {
    this.setData({ source: e.detail.value });
  },

  onBreedChange: function(e) {
    const index = e.detail.value;
    const selectedBreed = this.data.breedOptions[index];
    this.setData({ 
      breed: selectedBreed ? selectedBreed.value : '',
      breedLabel: selectedBreed ? selectedBreed.label : ''
    });
  },
  
  // 批次选择（用于称重和出栏记录）
  onBatchChange: function(e) {
    const index = e.detail.value;
    const selectedBatch = this.data.batchOptions[index];
    
    if (selectedBatch) {
      this.setData({ 
        batch: selectedBatch.value,
        batchLabel: selectedBatch.label,
        selectedBatchInfo: selectedBatch
      });
      
      // 根据选择的批次更新相关信息
      this.updateBatchRelatedInfo(selectedBatch);
    }
  },
  
  // 根据选择的批次更新相关信息
  updateBatchRelatedInfo: function(batchInfo) {
    if (this.data.recordType === 'weight') {
      // 称重记录：显示批次基本信息
      this.setData({
        maxCount: batchInfo.currentCount,
        breed: batchInfo.breed,
        entryDate: batchInfo.entryDate
      });
    } else if (this.data.recordType === 'sale') {
      // 出栏记录：显示可出栏信息
      this.setData({
        maxCount: batchInfo.currentCount,
        breed: batchInfo.breed,
        entryDate: batchInfo.entryDate,
        avgWeight: batchInfo.avgWeight
      });
      
      // 重新计算预计收入
      this.calculateTotalIncome();
    }
  },

  onAgeInput: function(e) {
    this.setData({ age: e.detail.value });
  },

  onCostInput: function(e) {
    this.setData({ cost: e.detail.value });
  },

  onSupplierInput: function(e) {
    this.setData({ supplier: e.detail.value });
  },

  onPriceInput: function(e) {
    const price = parseFloat(e.detail.value) || 0;
    this.setData({ price: price });
    this.calculateTotalIncome();
  },

  onBuyerInput: function(e) {
    this.setData({ buyer: e.detail.value });
  },

  onNotesInput: function(e) {
    this.setData({ notes: e.detail.value });
  },

  // 计算总收入
  calculateTotalIncome: function() {
    const { count, price, weight } = this.data;
    if (count && price) {
      const totalIncome = count * price * (weight || 1);
      this.setData({ totalIncome: totalIncome.toFixed(2) });
    }
  },

  // 验证表单
  validateForm: function() {
    const { recordType, batch, date, count } = this.data;
    
    if (!batch.trim()) {
      wx.showToast({ title: '请填写批次号', icon: 'none' });
      return false;
    }
    
    if (!date) {
      wx.showToast({ title: '请选择日期', icon: 'none' });
      return false;
    }
    
    if (!count || count <= 0) {
      wx.showToast({ title: '请填写数量', icon: 'none' });
      return false;
    }

    // 入栏记录特殊验证
    if (recordType === 'entry') {
      const { source, cost } = this.data;
      if (!source.trim()) {
        wx.showToast({ title: '请填写来源', icon: 'none' });
        return false;
      }
      if (!cost || parseFloat(cost) <= 0) {
        wx.showToast({ title: '请填写成本', icon: 'none' });
        return false;
      }
    }

    // 出栏记录特殊验证
    if (recordType === 'sale') {
      const { price, buyer } = this.data;
      if (!price || parseFloat(price) <= 0) {
        wx.showToast({ title: '请填写单价', icon: 'none' });
        return false;
      }
      if (!buyer.trim()) {
        wx.showToast({ title: '请填写买方', icon: 'none' });
        return false;
      }
    }

    return true;
  },

  // 保存记录
  onSave: async function() {
    if (!this.validateForm()) {
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    this.setData({ loading: true });

    const { recordType } = this.data;
    let recordData = this.buildRecordData();

    try {
      // 1. 首先保存生产记录（模拟API调用）
      await this.saveProductionRecord(recordData);
      
      // 2. 根据记录类型自动创建财务记录
      const financeResult = await this.createLinkedFinanceRecord(recordData);
      
      // 隐藏加载提示
      wx.hideLoading();
      this.setData({ loading: false });
      
      // 3. 显示保存结果
      let successMessage = '生产记录保存成功';
      if (financeResult.success) {
        successMessage += '\n' + financeResult.message;
      }
      
      wx.showModal({
        title: '保存成功',
        content: successMessage,
        showCancel: false,
        confirmText: '确定',
        success: () => {
          // 返回上一页并刷新数据
          wx.navigateBack();
        }
      });
      
    } catch (error) {
      // 隐藏加载提示
      wx.hideLoading();
      this.setData({ loading: false });
      
      wx.showModal({
        title: '保存失败',
        content: error.message || '保存记录时发生错误，请重试',
        showCancel: false
      });
    }
  },

  // 构建记录数据
  buildRecordData: function() {
    const { recordType, batch, date, count, weight, notes } = this.data;
    
    let recordData = {
      type: recordType,
      batch,
      date,
      count: parseInt(count),
      weight: parseFloat(weight) || 0,
      notes,
      createTime: new Date().toISOString()
    };

    // 根据记录类型添加特殊字段
    switch (recordType) {
    case 'entry':
      const { source, breed, age, cost, supplier } = this.data;
      recordData.details = {
        source,
        breed,
        age: parseInt(age) || 0,
        cost: parseFloat(cost) || 0,
        supplier
      };
      break;
        
    case 'sale':
      const { price, buyer, totalIncome } = this.data;
      recordData.details = {
        price: parseFloat(price) || 0,
        buyer,
        totalIncome: parseFloat(totalIncome) || 0
      };
      break;
        
    case 'weight':
      recordData.details = {
        averageWeight: parseFloat(weight) || 0,
        weightDate: date
      };
      break;
    }

    return recordData;
  },
  
  // 保存生产记录到数据库（模拟API调用）
  saveProductionRecord: async function(recordData) {
    return new Promise((resolve, reject) => {
      // 模拟网络延迟
      setTimeout(() => {
        try {
          // 生成记录ID
          recordData.id = `${recordData.type}_${recordData.batch}_${Date.now()}`;
          
          // 保存到本地存储（实际应用中应该调用API）
          const existingRecords = wx.getStorageSync('production_records') || [];
          existingRecords.unshift(recordData);
          wx.setStorageSync('production_records', existingRecords);
          
          // 如果是入栏记录，保存批次信息到已存在批次列表
          if (recordData.type === 'entry') {
            const existingBatches = wx.getStorageSync('existing_batches') || [];
            if (!existingBatches.includes(recordData.batch)) {
              existingBatches.push(recordData.batch);
              wx.setStorageSync('existing_batches', existingBatches);
            }
          }
          
          resolve(recordData);
        } catch (error) {
          reject(new Error('保存生产记录失败：' + error.message));
        }
      }, 800);
    });
  },
  
  // 创建关联的财务记录和批次管理
  createLinkedFinanceRecord: async function(recordData) {
    try {
      let financeResult = { success: true, message: '无需创建财务记录' };
      let batchResult = { success: true, message: '批次管理操作完成' };
      
      switch (recordData.type) {
      case 'entry':
        // 入栏记录：创建批次 + 财务支出
          
        // 1. 创建新批次
        batchResult = await BatchManagement.createBatch({
          batch: recordData.batch,
          date: recordData.date,
          count: recordData.count,
          breed: recordData.details.breed,
          age: recordData.details.age,
          costPerUnit: recordData.details.cost,
          supplier: recordData.details.supplier,
          source: recordData.details.source
        });
          
        // 2. 创建财务支出
        financeResult = await FinanceService.createEntryExpense({
          id: recordData.id,
          batch: recordData.batch,
          date: recordData.date,
          count: recordData.count,
          breed: recordData.details.breed,
          age: recordData.details.age,
          costPerUnit: recordData.details.cost,
          supplier: recordData.details.supplier,
          source: recordData.details.source
        });
        break;
          
      case 'sale':
        // 出栏记录：更新批次状态 + 创建财务收入
          
        // 1. 处理批次出栏
        batchResult = await BatchManagement.processBatchSale(recordData.batch, {
          date: recordData.date,
          count: recordData.count,
          weight: recordData.weight,
          price: recordData.details.price,
          buyer: recordData.details.buyer,
          notes: recordData.notes
        });
          
        // 2. 创建财务收入
        financeResult = await FinanceService.createSaleIncome({
          id: recordData.id,
          batch: recordData.batch,
          date: recordData.date,
          count: recordData.count,
          weight: recordData.weight,
          price: recordData.details.price,
          buyer: recordData.details.buyer,
          breed: this.data.selectedBatchInfo?.breed || 'unknown'
        });
        break;
          
      case 'weight':
        // 称重记录：更新批次称重信息
          
        batchResult = await BatchManagement.updateBatchWeight(recordData.batch, {
          date: recordData.date,
          weight: recordData.weight,
          count: recordData.count,
          notes: recordData.notes
        });
          
        financeResult = { 
          success: true, 
          message: '称重记录已保存' 
        };
        break;
          
      default:
        financeResult = { 
          success: true, 
          message: '记录类型未知，跳过财务记录创建' 
        };
      }
      
      // 合并结果消息
      let messages = [];
      if (batchResult.success && batchResult.message !== '批次管理操作完成') {
        messages.push(batchResult.message);
      }
      if (financeResult.success && financeResult.message !== '无需创建财务记录') {
        messages.push(financeResult.message);
      }
      
      return {
        success: batchResult.success && financeResult.success,
        message: messages.length > 0 ? messages.join('\n') : '记录保存完成',
        batchResult: batchResult,
        financeResult: financeResult
      };
      
    } catch (error) {
      // 财务记录创建失败不应该阻止生产记录的保存
      console.error('创建关联记录失败:', error);
      return {
        success: false,
        message: '关联记录创建失败，但生产记录已保存'
      };
    }
  },

  // 取消操作
  onCancel: function() {
    wx.navigateBack();
  }
});
