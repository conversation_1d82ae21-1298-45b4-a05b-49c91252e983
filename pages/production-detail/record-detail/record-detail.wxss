/* pages/production-detail/record-detail/record-detail.wxss */
.record-detail-container {
  min-height: 100vh;
  background-color: #f5f6f8;
}

/* ==================== 表单样式 ==================== */

.form-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background-color: var(--bg-primary);
  box-sizing: border-box;
}

/* 选择器显示样式 */
.picker-display {
  padding: var(--space-sm) var(--space-md);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  font-size: var(--text-base);
  color: var(--text-primary);
  min-height: 80rpx;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}

.picker-display .placeholder {
  color: var(--text-tertiary);
}

.picker-display:hover {
  border-color: var(--primary-300);
  background-color: #f8f9fa;
}

.picker-display::after {
  content: '';
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid var(--text-tertiary);
  pointer-events: none;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  font-size: 28rpx;
  color: #999999;
}

.record-content {
  padding: 20rpx;
}

/* 查看模式 */
.view-mode {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  margin-right: 20rpx;
}

.record-status {
  font-size: 24rpx;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

.status-death {
  background-color: #f5f5f5;
  color: #666666;
}

.status-sick {
  background-color: #ffe6e6;
  color: #ff3333;
}

.status-treated {
  background-color: #fff3e6;
  color: #ff9900;
}

.status-vaccination {
  background-color: #e6f7ff;
  color: #0066cc;
}

.status-entry {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-treatment {
  background-color: #fff0f6;
  color: #eb2f96;
}

.record-meta {
  margin-bottom: 30rpx;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.meta-item .label {
  font-size: 28rpx;
  color: #666666;
}

.meta-item .value {
  font-size: 28rpx;
  color: #333333;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.description-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  white-space: pre-wrap;
}

.record-images {
  margin: 30rpx 0;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 50rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.edit-btn {
  background-color: #0066cc;
  color: #ffffff;
  border: none;
}

.delete-btn {
  background-color: #ffffff;
  color: #ff3333;
  border: 1rpx solid #ff3333;
}

/* 编辑模式 */
.edit-mode {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item .label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 15rpx;
}

.form-item .input {
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.form-item .picker {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333333;
}

.status-options {
  display: flex;
  gap: 20rpx;
}

.status-option {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #666666;
}

.status-option.selected {
  background-color: #0066cc;
  color: #ffffff;
}

.form-item .textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.images-upload {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.uploaded-image {
  position: relative;
  width: 180rpx;
  height: 180rpx;
}

.uploaded-image image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.delete-image {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #ff3333;
  color: #ffffff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button {
  width: 180rpx;
  height: 180rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-button image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.upload-button text {
  font-size: 24rpx;
  color: #999999;
}

.form-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 50rpx;
}

.form-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background-color: #ffffff;
  color: #333333;
  border: 1rpx solid #e5e5e5;
}

.save-btn {
  background-color: #0066cc;
  color: #ffffff;
  border: none;
}

/* ==================== 状态特定字段样式 ==================== */

/* 状态特定字段容器 */
.status-specific-fields {
  background-color: #ffffff;
  border: 2rpx solid #e8f4fd;
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-top: var(--space-md);
}

.status-specific-fields .label {
  color: var(--primary-600);
  font-weight: var(--font-semibold);
  font-size: var(--text-lg);
  margin-bottom: var(--space-md);
}

/* 死亡状态字段 */
.death-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.death-fields .field-item:first-child {
  border-left: 4rpx solid #8b5a3c;
  background: linear-gradient(to right, #f5f1ed, #ffffff);
}

/* 疾病状态字段 */
.disease-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.disease-fields .field-item:first-child {
  border-left: 4rpx solid #dc3545;
  background: linear-gradient(to right, #fef2f2, #ffffff);
}

/* 治疗状态字段 */
.treatment-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.treatment-fields .field-item:first-child {
  border-left: 4rpx solid #28a745;
  background: linear-gradient(to right, #f0f9f0, #ffffff);
}

/* 入栏记录字段 */
.entry-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.entry-fields .field-item:first-child {
  border-left: 4rpx solid #52c41a;
  background: linear-gradient(to right, #f6ffed, #ffffff);
}

/* 防疫记录字段 */
.vaccination-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.vaccination-fields .field-item:first-child {
  border-left: 4rpx solid #0066cc;
  background: linear-gradient(to right, #e6f7ff, #ffffff);
}

/* 疾病治疗合并字段 */
.disease-treatment-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

/* 字段组样式 */
.field-group {
  background-color: #fafafa;
  border: 1rpx solid #f0f0f0;
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.group-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  padding-bottom: var(--space-sm);
  border-bottom: 2rpx solid #e0e0e0;
  position: relative;
}

.group-title::before {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 60rpx;
  height: 2rpx;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
}

/* 字段项通用样式 */
.field-item {
  padding: var(--space-md);
  border-radius: var(--radius-md);
  background-color: #fafafa;
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.field-item:hover {
  border-color: var(--primary-300);
  box-shadow: 0 2rpx 8rpx rgba(0, 102, 204, 0.1);
}

.field-label {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
  font-weight: var(--font-medium);
}

/* 表单文本域样式 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: var(--space-sm) var(--space-md);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background-color: var(--bg-primary);
  line-height: 1.5;
  box-sizing: border-box;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.form-textarea:focus {
  border-color: var(--primary-500);
  outline: none;
}



/* 状态指示器 */
.status-specific-fields::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  height: 6rpx;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.status-specific-fields {
  position: relative;
}

/* 死亡状态指示器 - 修复WXSS兼容性 */
.death-fields .status-specific-fields::before {
  background: linear-gradient(90deg, #8b5a3c, #a0795c);
}

/* 疾病状态指示器 - 修复WXSS兼容性 */
.disease-fields .status-specific-fields::before {
  background: linear-gradient(90deg, #dc3545, #e85563);
}

/* 治疗状态指示器 - 修复WXSS兼容性 */
.treatment-fields .status-specific-fields::before {
  background: linear-gradient(90deg, #28a745, #48c75a);
}

/* 响应式布局 */
@media screen and (max-width: 750rpx) {
  .field-item {
    padding: var(--space-sm);
  }
  
  .form-textarea {
    min-height: 100rpx;
  }
}