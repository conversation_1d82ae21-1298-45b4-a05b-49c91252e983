<!--pages/production-detail/knowledge/knowledge.wxml-->
<view class="knowledge-container">
  <!-- 顶部搜索区域 -->
  <view class="search-section">
    <view wx:if="{{!showSearch}}" class="search-bar" bindtap="onShowSearch">
      <icon class="search-icon" type="search" size="16" color="#999"/>
      <text class="search-placeholder">搜索知识库内容</text>
    </view>
    <view wx:else class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索知识库内容" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        focus="{{showSearch}}"
      />
      <text class="cancel-btn" bindtap="onHideSearch">取消</text>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
      <view class="category-list">
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="category-item {{currentCategory === item.id ? 'active' : ''}}"
          data-category="{{item.id}}"
          bindtap="onCategoryChange"
        >
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 文章列表 -->
  <view class="article-section">
    <view wx:if="{{loading}}" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>
    
    <view wx:elif="{{articles.length === 0}}" class="empty-container">
      <image class="empty-icon" src="/images/icons/search.png"/>
      <text class="empty-text">暂无相关内容</text>
    </view>
    
    <view wx:else class="article-list">
      <view 
        wx:for="{{articles}}" 
        wx:key="id"
        class="article-item"
        data-id="{{item.id}}"
        bindtap="onArticleClick"
      >
        <view class="article-header">
          <text class="article-title">{{item.title}}</text>
          <view class="article-meta">
            <text class="category-tag">{{item.categoryName}}</text>
            <text class="read-count">{{item.readCount}}次阅读</text>
          </view>
        </view>
        
        <text class="article-summary">{{item.summary}}</text>
        
        <view class="article-footer">
          <text class="publish-time">{{item.publishTime}}</text>
          <text class="author">{{item.author}}</text>
        </view>
        
        <view wx:if="{{item.tags && item.tags.length > 0}}" class="tag-list">
          <text 
            wx:for="{{item.tags}}" 
            wx:for-item="tag" 
            wx:key="*this"
            class="tag"
          >
            {{tag}}
          </text>
        </view>
      </view>
    </view>
  </view>
</view>