// pages/production-detail/knowledge/detail/detail.js
const { getArticleById } = require('../../../../utils/knowledge-data.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    article: null,
    loading: true,
    showFullContent: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.loadArticle(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 加载文章详情
   */
  loadArticle(id) {
    wx.showLoading({
      title: '加载中...'
    });

    try {
      const article = getArticleById(id);
      
      if (article) {
        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: article.title.length > 10 ? article.title.substring(0, 10) + '...' : article.title
        });

        this.setData({
          article: article,
          loading: false
        });
      } else {
        wx.showToast({
          title: '文章不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }

      wx.hideLoading();
    } catch (error) {
      console.error('加载文章失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 展开/收起全文
   */
  onToggleContent() {
    this.setData({
      showFullContent: !this.data.showFullContent
    });
  },

  /**
   * 复制内容
   */
  onCopyContent() {
    const { article } = this.data;
    if (article && article.content) {
      // 移除HTML标签，只保留文本内容
      const textContent = article.content.replace(/<[^>]*>/g, '').replace(/\n\s*\n/g, '\n').trim();
      
      wx.setClipboardData({
        data: textContent,
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        }
      });
    }
  },

  /**
   * 分享文章
   */
  onShareArticle() {
    const { article } = this.data;
    return {
      title: article ? article.title : '智慧养鹅知识库',
      path: `/pages/production-detail/knowledge/detail/detail?id=${article.id}`,
      imageUrl: '/images/logo.png'
    };
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.article) {
      this.loadArticle(this.data.article.id);
    }
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.onShareArticle();
  },

  /**
   * 返回上一页
   */
  onGoBack() {
    wx.navigateBack();
  }
})