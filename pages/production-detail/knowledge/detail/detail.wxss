/* pages/production-detail/knowledge/detail/detail.wxss */
.detail-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
}

/* 文章容器 */
.article-wrapper {
  padding: 20rpx 30rpx 100rpx;
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 文章标题区域 */
.article-header {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  border: 3rpx solid #ff6b6b;
  position: relative;
  overflow: hidden;
}

.article-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #0066CC, #4A90E2, #0066CC);
  background-size: 200% 100%;
  animation: headerGradient 3s ease-in-out infinite;
}

@keyframes headerGradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.article-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.5;
  display: block;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}



.section-title {
  font-size: 26rpx;
  font-weight: 700;
  color: #333;
  display: block;
  margin-bottom: 25rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 20rpx;
  background: linear-gradient(135deg, #0066CC, #4A90E2);
  border-radius: 3rpx;
}

/* 文章内容 */
.article-content {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  border: 3rpx solid #4ecdc4;
}

.content-wrapper {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.content-wrapper.collapsed {
  max-height: 600rpx;
}

.content-wrapper.collapsed::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: linear-gradient(transparent, white);
  pointer-events: none;
}

.content-wrapper.expanded {
  max-height: none;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  letter-spacing: 0.3rpx;
  text-align: justify;
}

/* 内容操作区 */
.content-actions {
  text-align: center;
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid rgba(0, 0, 0, 0.05);
}

.toggle-btn {
  background: linear-gradient(135deg, #0066CC 0%, #4A90E2 50%, #0066CC 100%);
  background-size: 200% 100%;
  color: white;
  border: 3rpx solid #ffd93d;
  border-radius: 40rpx;
  padding: 18rpx 36rpx;
  font-size: 24rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(0, 102, 204, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.toggle-btn:active::before {
  left: 100%;
}

.toggle-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.4);
  background-position: 100% 0;
}

/* 底部操作区 */
.action-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 15rpx;
  border: 3rpx solid #a8e6cf;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border: 2rpx solid #b39ddb;
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  font-size: 20rpx;
  color: #555;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn:active::before {
  opacity: 1;
}

.action-btn:active {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transform: translateY(3rpx) scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

.copy-btn:active {
  border-color: #0066CC;
  color: #0066CC;
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.05), rgba(0, 102, 204, 0.02));
}

.share-btn:active {
  border-color: #ff6b35;
  color: #ff6b35;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 107, 53, 0.02));
}

.btn-icon {
  width: 28rpx;
  height: 28rpx;
  transition: transform 0.3s ease;
}

.action-btn:active .btn-icon {
  transform: scale(1.1);
}

.btn-text {
  font-size: 20rpx;
  font-weight: 600;
}

.copy-btn {
  border-color: #ff8a65 !important;
}

.share-btn {
  border-color: #81c784 !important;
}

/* 空状态 */
.empty-container, .loading-container {
  text-align: center;
  padding: 200rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 20rpx;
  margin: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 40rpx;
  opacity: 0.7;
}

.empty-text, .loading-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 50rpx;
  display: block;
  font-weight: 500;
}

.back-btn {
  background: linear-gradient(135deg, #0066CC, #4A90E2);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 25rpx rgba(0, 102, 204, 0.3);
  transition: all 0.3s ease;
}

.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.4);
}

/* 加载动画 */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.loading-text {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 320px) {
  .meta-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 15rpx;
  }
  
  .action-section {
    flex-direction: column;
    gap: 20rpx;
  }
  
  .article-title {
    font-size: 36rpx;
  }
  
  .content-text {
    font-size: 30rpx;
  }
  
  .article-wrapper {
    padding: 15rpx 20rpx 100rpx;
  }
  
  .article-header, .article-summary, .tag-section, .article-content, .action-section {
    margin-left: 0;
    margin-right: 0;
    border-radius: 15rpx;
  }
}