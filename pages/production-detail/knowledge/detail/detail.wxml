<!--pages/production-detail/knowledge/detail/detail.wxml-->
<view class="detail-container" wx:if="{{!loading}}">
  <view class="article-wrapper" wx:if="{{article}}">
    <!-- 文章头部信息 -->
    <view class="article-header">
      <text class="article-title">{{article.title}}</text>
    </view>



    <!-- 文章内容 -->
    <view class="article-content">
      <text class="section-title">详细内容</text>
      
      <view class="content-wrapper {{showFullContent ? 'expanded' : 'collapsed'}}">
        <rich-text class="content-text" nodes="{{article.content}}"></rich-text>
      </view>
      
      <view class="content-actions">
        <button class="toggle-btn" bindtap="onToggleContent">
          {{showFullContent ? '收起' : '展开全文'}}
        </button>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="action-section">
      <button class="action-btn copy-btn" bindtap="onCopyContent">
        <image class="btn-icon" src="/images/icons/edit.svg"/>
        <text class="btn-text">复制内容</text>
      </button>
      
      <button class="action-btn share-btn" open-type="share">
        <image class="btn-icon" src="/images/icons/star.svg"/>
        <text class="btn-text">分享文章</text>
      </button>
    </view>
  </view>

  <!-- 文章不存在 -->
  <view wx:else class="empty-container">
    <image class="empty-icon" src="/images/icons/search.png"/>
    <text class="empty-text">文章不存在或已被删除</text>
    <button class="back-btn" bindtap="onGoBack">返回</button>
  </view>
</view>

<!-- 加载中 -->
<view wx:else class="loading-container">
  <text class="loading-text">加载中...</text>
</view>