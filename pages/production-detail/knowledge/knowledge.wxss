/* pages/production-detail/knowledge/knowledge.wxss */
.knowledge-container {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 30rpx 30rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
}

.search-icon {
  margin-right: 15rpx;
}

.search-placeholder {
  font-size: 28rpx;
  color: #999;
  flex: 1;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.cancel-btn {
  font-size: 28rpx;
  color: #0066CC;
  padding: 10rpx;
}

/* 分类区域 */
.category-section {
  background: white;
  padding: 0 0 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 30rpx;
  gap: 30rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  background: #f8f9fa;
  border-radius: 40rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.category-item.active {
  color: white;
  background: #0066CC;
}

/* 文章区域 */
.article-section {
  padding: 30rpx;
}

.loading-container, .empty-container {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 文章列表 */
.article-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.article-item {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.article-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
}

/* 文章标题区域 */
.article-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.article-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  flex: 1;
  margin-right: 20rpx;
}

.article-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex-shrink: 0;
  gap: 10rpx;
}

.category-tag {
  font-size: 22rpx;
  color: #0066CC;
  background: rgba(0, 102, 204, 0.1);
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

.read-count {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
}

/* 文章摘要 */
.article-summary {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 25rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

/* 文章底部信息 */
.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.author {
  font-size: 24rpx;
  color: #0066CC;
}

/* 标签列表 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.tag {
  font-size: 22rpx;
  color: #666;
  background: #f8f9fa;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
  border: 1rpx solid #e0e0e0;
}

/* 响应式设计 */
@media (max-width: 320px) {
  .article-header {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .article-meta {
    align-items: flex-start;
    flex-direction: row;
    gap: 20rpx;
  }
  
  .article-title {
    margin-right: 0;
  }
}