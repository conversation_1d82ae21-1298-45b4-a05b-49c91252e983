// pages/production-detail/knowledge/knowledge.js
const { getAllArticles, getCategories, getArticlesByCategory, searchArticles } = require('../../../utils/knowledge-data.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    articles: [],
    categories: [],
    currentCategory: 'all',
    searchKeyword: '',
    loading: true,
    showSearch: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    wx.setNavigationBarTitle({
      title: '知识库'
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 加载数据
   */
  loadData() {
    wx.showLoading({
      title: '加载中...'
    });

    try {
      const categories = getCategories();
      const articles = getAllArticles();

      this.setData({
        categories: categories,
        articles: articles,
        loading: false
      });

      wx.hideLoading();
    } catch (error) {
      console.error('加载知识库数据失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分类切换
   */
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category;
    const articles = getArticlesByCategory(category);
    
    this.setData({
      currentCategory: category,
      articles: articles
    });
  },

  /**
   * 显示搜索框
   */
  onShowSearch() {
    this.setData({
      showSearch: true
    });
  },

  /**
   * 隐藏搜索框
   */
  onHideSearch() {
    this.setData({
      showSearch: false,
      searchKeyword: ''
    });
    // 重新加载所有文章
    this.onCategoryChange({ currentTarget: { dataset: { category: this.data.currentCategory } } });
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    // 实时搜索
    if (keyword.trim()) {
      const results = searchArticles(keyword, this.data.currentCategory);
      this.setData({
        articles: results
      });
    } else {
      // 清空搜索时恢复分类文章
      this.onCategoryChange({ currentTarget: { dataset: { category: this.data.currentCategory } } });
    }
  },

  /**
   * 点击文章项
   */
  onArticleClick(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `./detail/detail?id=${id}`
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadData();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '智慧养鹅知识库',
      path: '/pages/production-detail/knowledge/knowledge'
    };
  }
})