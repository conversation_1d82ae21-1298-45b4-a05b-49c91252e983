<testsuites id="" name="" tests="4" failures="0" skipped="0" errors="0" time="25.793010000000002">
<testsuite name="fixed-button-test.spec.js" timestamp="2025-08-25T15:58:08.805Z" hostname="chromium" tests="4" failures="0" skipped="0" time="78.935" errors="0">
<testcase name="修复后的按钮功能测试 › 测试修复后的新增用户按钮" classname="fixed-button-test.spec.js" time="17.955">
<system-out>
<![CDATA[=== 测试修复后的新增用户功能 ===
当前页面URL: http://localhost:4000/saas-admin/users
新增用户按钮是否存在: [33mtrue[39m
点击新增用户按钮...
模态框是否显示: [33mfalse[39m
❌ 模态框显示失败
]]>
</system-out>
</testcase>
<testcase name="修复后的按钮功能测试 › 测试用户状态切换功能" classname="fixed-button-test.spec.js" time="22.892">
<system-out>
<![CDATA[=== 测试用户状态切换功能 ===
用户列表中有 0 行数据
⚠️ 用户列表为空，无法测试状态切换功能
]]>
</system-out>
</testcase>
<testcase name="修复后的按钮功能测试 › 测试导出功能" classname="fixed-button-test.spec.js" time="17.321">
<system-out>
<![CDATA[=== 测试用户导出功能 ===
导出用户按钮是否存在: [33mtrue[39m
导出按钮的onclick属性: exportUsers()
✅ 导出功能已实现
]]>
</system-out>
</testcase>
<testcase name="修复后的按钮功能测试 › 测试API数据加载" classname="fixed-button-test.spec.js" time="20.767">
<system-out>
<![CDATA[=== 测试API数据加载情况 ===
捕获到 0 个API请求:
❌ 页面没有调用任何API
总用户数显示: -
❌ 统计数据未更新
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>