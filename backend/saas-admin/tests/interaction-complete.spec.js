const { test, expect } = require('@playwright/test');

const testAdmin = {
  username: 'admin',
  password: 'admin123'
};

test.describe('SAAS后台管理系统 - 全面交互测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  test('测试所有页面的按钮交互', async ({ page }) => {
    const pages = [
      { name: '仪表板', url: '/saas-admin/dashboard' },
      { name: '租户管理', url: '/saas-admin/tenants' },
      { name: '数据监控', url: '/saas-admin/cross-tenant-data' },
      { name: '平台用户', url: '/saas-admin/users' },
      { name: '订阅计划', url: '/saas-admin/plans' },
      { name: '价格管理', url: '/saas-admin/pricing' },
      { name: 'AI配置', url: '/saas-admin/ai-config' },
      { name: '知识库', url: '/saas-admin/knowledge' },
      { name: '公告管理', url: '/saas-admin/announcements' },
      { name: '系统监控', url: '/saas-admin/monitoring' },
      { name: '系统日志', url: '/saas-admin/logs' }
    ];

    for (const pageInfo of pages) {
      console.log(`测试 ${pageInfo.name} 页面...`);
      
      // 导航到页面
      await page.goto(pageInfo.url);
      await page.waitForTimeout(1000);
      
      // 测试页面加载
      await expect(page).toHaveURL(pageInfo.url);
      
      // 查找并测试所有按钮
      const buttons = page.locator('button:visible');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        const button = buttons.nth(i);
        const buttonText = await button.textContent();
        
        // 跳过某些可能导致页面离开的按钮
        if (!buttonText || 
            buttonText.includes('退出') || 
            buttonText.includes('删除') || 
            buttonText.includes('logout')) {
          continue;
        }
        
        try {
          // 检查按钮是否可点击
          if (await button.isEnabled()) {
            // 模拟鼠标悬停
            await button.hover();
            await page.waitForTimeout(100);
            
            // 记录按钮存在且可交互
            console.log(`- 发现可交互按钮: "${buttonText}"`);
          }
        } catch (error) {
          console.log(`- 按钮 "${buttonText}" 交互异常: ${error.message}`);
        }
      }
      
      // 测试链接
      const links = page.locator('a:visible');
      const linkCount = await links.count();
      
      for (let i = 0; i < Math.min(linkCount, 5); i++) {
        const link = links.nth(i);
        const linkText = await link.textContent();
        
        if (!linkText || 
            linkText.includes('退出') || 
            linkText.includes('logout')) {
          continue;
        }
        
        try {
          await link.hover();
          await page.waitForTimeout(50);
          console.log(`- 发现可交互链接: "${linkText}"`);
        } catch (error) {
          console.log(`- 链接 "${linkText}" 交互异常: ${error.message}`);
        }
      }
      
      // 测试表单输入框
      const inputs = page.locator('input:visible, textarea:visible, select:visible');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < Math.min(inputCount, 5); i++) {
        const input = inputs.nth(i);
        
        try {
          const inputType = await input.getAttribute('type');
          const inputName = await input.getAttribute('name');
          const placeholder = await input.getAttribute('placeholder');
          
          if (inputType !== 'hidden' && await input.isEnabled()) {
            await input.focus();
            await page.waitForTimeout(50);
            
            console.log(`- 发现可交互输入框: type="${inputType}", name="${inputName}", placeholder="${placeholder}"`);
          }
        } catch (error) {
          console.log(`- 输入框交互异常: ${error.message}`);
        }
      }
    }
  });

  test('测试表单提交功能', async ({ page }) => {
    const formsToTest = [
      {
        page: '/saas-admin/users',
        name: '用户管理',
        addButtonText: '添加',
        formFields: [
          { name: 'username', value: 'testuser_' + Date.now() },
          { name: 'nickname', value: '测试用户' },
          { name: 'phone', value: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0') },
          { name: 'email', value: 'test' + Date.now() + '@test.com' }
        ]
      },
      {
        page: '/saas-admin/plans',
        name: '订阅计划',
        addButtonText: '添加',
        formFields: [
          { name: 'name', value: '测试计划_' + Date.now() },
          { name: 'code', value: 'test_plan_' + Date.now() },
          { name: 'description', value: '这是一个测试订阅计划' },
          { name: 'price_monthly', value: '99.99' }
        ]
      },
      {
        page: '/saas-admin/announcements',
        name: '公告管理',
        addButtonText: '添加',
        formFields: [
          { name: 'title', value: '测试公告_' + Date.now() },
          { name: 'content', value: '这是一个测试公告内容' }
        ]
      }
    ];

    for (const formTest of formsToTest) {
      console.log(`测试 ${formTest.name} 表单提交...`);
      
      await page.goto(formTest.page);
      await page.waitForTimeout(1000);
      
      // 查找添加按钮
      const addButton = page.locator(`button:has-text("${formTest.addButtonText}"), button:has-text("新增"), .btn-add`);
      
      if (await addButton.count() > 0) {
        await addButton.first().click();
        await page.waitForTimeout(500);
        
        // 填写表单字段
        let fieldsFound = 0;
        for (const field of formTest.formFields) {
          const input = page.locator(`input[name="${field.name}"], textarea[name="${field.name}"]`);
          
          if (await input.count() > 0) {
            await input.first().fill(field.value);
            fieldsFound++;
            console.log(`- 填写字段 ${field.name}: ${field.value}`);
          }
        }
        
        if (fieldsFound > 0) {
          // 查找提交按钮
          const submitButton = page.locator('button[type="submit"], button:has-text("确定"), button:has-text("保存"), button:has-text("提交")');
          
          if (await submitButton.count() > 0) {
            await submitButton.first().click();
            await page.waitForTimeout(1000);
            
            console.log(`- ${formTest.name} 表单提交完成`);
            
            // 检查是否有成功消息或页面更新
            const successMessage = page.locator('.alert-success, .success-message, .toast-success');
            if (await successMessage.count() > 0) {
              console.log(`- 检测到成功消息`);
            }
          } else {
            console.log(`- ${formTest.name} 未找到提交按钮`);
          }
        } else {
          console.log(`- ${formTest.name} 未找到表单字段`);
        }
      } else {
        console.log(`- ${formTest.name} 未找到添加按钮`);
      }
    }
  });

  test('测试搜索和筛选功能', async ({ page }) => {
    const searchPages = [
      { name: '租户管理', url: '/saas-admin/tenants' },
      { name: '平台用户', url: '/saas-admin/users' },
      { name: '订阅计划', url: '/saas-admin/plans' },
      { name: '知识库', url: '/saas-admin/knowledge' },
      { name: '公告管理', url: '/saas-admin/announcements' },
      { name: '系统日志', url: '/saas-admin/logs' }
    ];

    for (const pageInfo of searchPages) {
      console.log(`测试 ${pageInfo.name} 搜索功能...`);
      
      await page.goto(pageInfo.url);
      await page.waitForTimeout(1000);
      
      // 测试搜索输入框
      const searchInput = page.locator('input[placeholder*="搜索"], input[type="search"], input[name="search"], .search-input');
      
      if (await searchInput.count() > 0) {
        const searchTerm = 'test';
        await searchInput.first().fill(searchTerm);
        await page.keyboard.press('Enter');
        await page.waitForTimeout(1000);
        
        console.log(`- ${pageInfo.name} 搜索功能正常，搜索词: ${searchTerm}`);
        
        // 清空搜索
        await searchInput.first().clear();
        await page.keyboard.press('Enter');
        await page.waitForTimeout(500);
      } else {
        console.log(`- ${pageInfo.name} 未找到搜索输入框`);
      }
      
      // 测试筛选下拉框
      const filterSelects = page.locator('select');
      const selectCount = await filterSelects.count();
      
      for (let i = 0; i < Math.min(selectCount, 3); i++) {
        const select = filterSelects.nth(i);
        
        try {
          const options = page.locator(`select:nth-of-type(${i + 1}) option`);
          const optionCount = await options.count();
          
          if (optionCount > 1) {
            await select.selectOption({ index: 1 });
            await page.waitForTimeout(500);
            console.log(`- ${pageInfo.name} 筛选下拉框 ${i + 1} 功能正常`);
            
            // 重置为默认选项
            await select.selectOption({ index: 0 });
            await page.waitForTimeout(300);
          }
        } catch (error) {
          console.log(`- ${pageInfo.name} 筛选下拉框 ${i + 1} 异常: ${error.message}`);
        }
      }
    }
  });

  test('测试数据表格交互功能', async ({ page }) => {
    const tablePages = [
      { name: '租户管理', url: '/saas-admin/tenants' },
      { name: '平台用户', url: '/saas-admin/users' },
      { name: '订阅计划', url: '/saas-admin/plans' },
      { name: '系统日志', url: '/saas-admin/logs' }
    ];

    for (const pageInfo of tablePages) {
      console.log(`测试 ${pageInfo.name} 表格交互...`);
      
      await page.goto(pageInfo.url);
      await page.waitForTimeout(1000);
      
      // 查找表格
      const table = page.locator('table');
      
      if (await table.count() > 0) {
        // 测试表头排序（如果有）
        const sortableHeaders = page.locator('table th[data-sort], table th.sortable, table th:has(.sort-icon)');
        const headerCount = await sortableHeaders.count();
        
        for (let i = 0; i < Math.min(headerCount, 3); i++) {
          const header = sortableHeaders.nth(i);
          
          try {
            await header.click();
            await page.waitForTimeout(500);
            console.log(`- ${pageInfo.name} 表头排序 ${i + 1} 功能正常`);
          } catch (error) {
            console.log(`- ${pageInfo.name} 表头排序 ${i + 1} 异常: ${error.message}`);
          }
        }
        
        // 测试表格行操作按钮
        const actionButtons = page.locator('table tbody tr button, table tbody tr a.btn');
        const actionCount = await actionButtons.count();
        
        for (let i = 0; i < Math.min(actionCount, 5); i++) {
          const button = actionButtons.nth(i);
          const buttonText = await button.textContent();
          
          // 跳过删除按钮避免误删数据
          if (buttonText && !buttonText.includes('删除') && !buttonText.includes('Delete')) {
            try {
              await button.hover();
              await page.waitForTimeout(100);
              console.log(`- ${pageInfo.name} 表格操作按钮 "${buttonText}" 可交互`);
            } catch (error) {
              console.log(`- ${pageInfo.name} 操作按钮异常: ${error.message}`);
            }
          }
        }
        
        // 测试分页功能
        const pagination = page.locator('.pagination, .page-nav, [data-pagination]');
        
        if (await pagination.count() > 0) {
          const pageLinks = page.locator('.pagination a, .page-nav a');
          const linkCount = await pageLinks.count();
          
          if (linkCount > 0) {
            // 测试下一页按钮
            const nextButton = page.locator('.pagination .next, .pagination :text("下一页"), .pagination :text("Next")');
            
            if (await nextButton.count() > 0 && await nextButton.first().isEnabled()) {
              await nextButton.first().click();
              await page.waitForTimeout(1000);
              console.log(`- ${pageInfo.name} 分页下一页功能正常`);
              
              // 回到第一页
              const firstButton = page.locator('.pagination .first, .pagination :text("首页"), .pagination :text("1")');
              if (await firstButton.count() > 0) {
                await firstButton.first().click();
                await page.waitForTimeout(500);
              }
            }
          }
        }
        
      } else {
        console.log(`- ${pageInfo.name} 未找到数据表格`);
      }
    }
  });

  test('测试模态框和弹窗交互', async ({ page }) => {
    const modalPages = [
      { name: '平台用户', url: '/saas-admin/users', triggerText: '添加' },
      { name: '订阅计划', url: '/saas-admin/plans', triggerText: '添加' },
      { name: '公告管理', url: '/saas-admin/announcements', triggerText: '添加' }
    ];

    for (const pageInfo of modalPages) {
      console.log(`测试 ${pageInfo.name} 模态框交互...`);
      
      await page.goto(pageInfo.url);
      await page.waitForTimeout(1000);
      
      // 查找触发模态框的按钮
      const triggerButton = page.locator(`button:has-text("${pageInfo.triggerText}"), .btn:has-text("${pageInfo.triggerText}")`);
      
      if (await triggerButton.count() > 0) {
        await triggerButton.first().click();
        await page.waitForTimeout(500);
        
        // 查找模态框
        const modal = page.locator('.modal, .dialog, .popup, .overlay');
        
        if (await modal.count() > 0 && await modal.first().isVisible()) {
          console.log(`- ${pageInfo.name} 模态框打开成功`);
          
          // 测试关闭按钮
          const closeButton = page.locator('.modal .close, .modal .btn-close, .modal :text("取消"), .modal :text("关闭")');
          
          if (await closeButton.count() > 0) {
            await closeButton.first().click();
            await page.waitForTimeout(500);
            
            // 验证模态框是否关闭
            if (!await modal.first().isVisible()) {
              console.log(`- ${pageInfo.name} 模态框关闭功能正常`);
            }
          } else {
            // 尝试按 ESC 键关闭
            await page.keyboard.press('Escape');
            await page.waitForTimeout(500);
            console.log(`- ${pageInfo.name} 模态框 ESC 关闭测试完成`);
          }
        } else {
          console.log(`- ${pageInfo.name} 模态框未正确显示`);
        }
      } else {
        console.log(`- ${pageInfo.name} 未找到触发按钮`);
      }
    }
  });

  test('测试响应式设计和移动端兼容性', async ({ page }) => {
    const viewports = [
      { name: '桌面端', width: 1920, height: 1080 },
      { name: '平板端', width: 768, height: 1024 },
      { name: '手机端', width: 375, height: 667 }
    ];

    const testPages = [
      '/saas-admin/dashboard',
      '/saas-admin/users',
      '/saas-admin/plans'
    ];

    for (const viewport of viewports) {
      console.log(`测试 ${viewport.name} 响应式设计...`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      for (const pageUrl of testPages) {
        await page.goto(pageUrl);
        await page.waitForTimeout(1000);
        
        // 检查侧边栏是否适应视口
        const sidebar = page.locator('.sidebar');
        
        if (await sidebar.count() > 0) {
          const isVisible = await sidebar.first().isVisible();
          console.log(`- ${pageUrl} 在 ${viewport.name} 侧边栏可见: ${isVisible}`);
        }
        
        // 检查主要内容区域
        const mainContent = page.locator('.main-content, .content, main');
        
        if (await mainContent.count() > 0) {
          const isVisible = await mainContent.first().isVisible();
          console.log(`- ${pageUrl} 在 ${viewport.name} 主内容区可见: ${isVisible}`);
        }
        
        // 检查按钮是否可点击
        const buttons = page.locator('button:visible');
        const buttonCount = await buttons.count();
        
        if (buttonCount > 0) {
          console.log(`- ${pageUrl} 在 ${viewport.name} 发现 ${buttonCount} 个可见按钮`);
        }
      }
    }
    
    // 恢复默认视口大小
    await page.setViewportSize({ width: 1920, height: 1080 });
  });

});