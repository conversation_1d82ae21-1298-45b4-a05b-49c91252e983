const { test, expect } = require('@playwright/test');

// 测试数据
const testAdmin = {
  username: 'admin',
  password: 'admin123'
};

test.describe('SAAS后台管理系统 - 用户管理功能', () => {
  
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到用户管理
    await page.click('a[href="/saas-admin/users"]');
    await page.waitForURL('**/users');
  });

  test('应该显示用户管理界面', async ({ page }) => {
    await expect(page.locator('h1, .page-title')).toContainText(/用户|管理/);
    
    // 检查用户列表表格
    const userTable = page.locator('table, .user-list');
    if (await userTable.count() > 0) {
      await expect(userTable).toBeVisible();
    }
    
    // 检查添加用户按钮
    const addButton = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add');
    if (await addButton.count() > 0) {
      await expect(addButton.first()).toBeVisible();
    }
  });

  test('应该能够创建新用户', async ({ page }) => {
    // 点击添加用户按钮
    const addButton = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add');
    
    if (await addButton.count() > 0) {
      await addButton.first().click();
      
      // 等待模态框或表单出现
      await page.waitForTimeout(500);
      
      // 填写用户信息（根据实际表单字段调整）
      const usernameInput = page.locator('input[name="username"], input[placeholder*="用户名"]');
      if (await usernameInput.count() > 0) {
        const testUsername = 'testuser_' + Date.now();
        await usernameInput.first().fill(testUsername);
      }
      
      const nicknameInput = page.locator('input[name="nickname"], input[placeholder*="昵称"]');
      if (await nicknameInput.count() > 0) {
        await nicknameInput.first().fill('测试用户');
      }
      
      const phoneInput = page.locator('input[name="phone"], input[placeholder*="手机"]');
      if (await phoneInput.count() > 0) {
        const testPhone = '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
        await phoneInput.first().fill(testPhone);
      }
      
      const emailInput = page.locator('input[name="email"], input[placeholder*="邮箱"]');
      if (await emailInput.count() > 0) {
        const testEmail = 'test' + Date.now() + '@test.com';
        await emailInput.first().fill(testEmail);
      }
      
      // 提交表单
      const submitButton = page.locator('button[type="submit"], button:has-text("确定"), button:has-text("保存")');
      if (await submitButton.count() > 0) {
        await submitButton.first().click();
        
        // 等待成功消息或页面更新
        await page.waitForTimeout(1000);
      }
    }
  });

  test('应该能够搜索用户', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索"], input[type="search"], .search-input');
    
    if (await searchInput.count() > 0) {
      await searchInput.first().fill('admin');
      await page.keyboard.press('Enter');
      
      // 等待搜索结果
      await page.waitForTimeout(1000);
      
      // 验证搜索结果
      const tableRows = page.locator('table tbody tr, .user-item');
      if (await tableRows.count() > 0) {
        const firstRow = tableRows.first();
        await expect(firstRow).toContainText(/admin/i);
      }
    }
  });

  test('应该能够查看用户详情', async ({ page }) => {
    // 查找查看详情按钮
    const viewButtons = page.locator('button:has-text("查看"), .btn-view, a:has-text("详情")');
    
    if (await viewButtons.count() > 0) {
      await viewButtons.first().click();
      
      // 等待详情页面或模态框加载
      await page.waitForTimeout(500);
      
      // 验证详情页面存在用户信息
      const detailContent = page.locator('.modal-body, .detail-content, .user-detail');
      if (await detailContent.count() > 0) {
        await expect(detailContent.first()).toBeVisible();
      }
    }
  });

});

test.describe('SAAS后台管理系统 - 订阅计划管理', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到订阅计划管理
    await page.click('a[href="/saas-admin/plans"]');
    await page.waitForURL('**/plans');
  });

  test('应该显示订阅计划列表', async ({ page }) => {
    await expect(page.locator('h1, .page-title')).toContainText(/订阅|计划/);
    
    // 检查计划列表
    const planList = page.locator('table, .plan-list, .card-group');
    if (await planList.count() > 0) {
      await expect(planList).toBeVisible();
    }
  });

  test('应该能够创建新的订阅计划', async ({ page }) => {
    const addButton = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add');
    
    if (await addButton.count() > 0) {
      await addButton.first().click();
      await page.waitForTimeout(500);
      
      // 填写计划信息
      const nameInput = page.locator('input[name="name"], input[placeholder*="名称"]');
      if (await nameInput.count() > 0) {
        await nameInput.first().fill('测试计划_' + Date.now());
      }
      
      const codeInput = page.locator('input[name="code"], input[placeholder*="编码"]');
      if (await codeInput.count() > 0) {
        await codeInput.first().fill('test_plan_' + Date.now());
      }
      
      const descInput = page.locator('textarea[name="description"], input[name="description"]');
      if (await descInput.count() > 0) {
        await descInput.first().fill('这是一个测试订阅计划');
      }
      
      const monthlyPriceInput = page.locator('input[name="price_monthly"], input[placeholder*="月价格"]');
      if (await monthlyPriceInput.count() > 0) {
        await monthlyPriceInput.first().fill('99.99');
      }
      
      // 提交表单
      const submitButton = page.locator('button[type="submit"], button:has-text("确定"), button:has-text("保存")');
      if (await submitButton.count() > 0) {
        await submitButton.first().click();
        await page.waitForTimeout(1000);
      }
    }
  });

});

test.describe('SAAS后台管理系统 - AI配置管理', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到AI配置
    await page.click('a[href="/saas-admin/ai-config"]');
    await page.waitForURL('**/ai-config');
  });

  test('应该显示AI配置界面', async ({ page }) => {
    await expect(page.locator('h1, .page-title')).toContainText(/AI|配置/);
    
    // 检查配置列表或表单
    const aiConfigContent = page.locator('table, .config-list, .ai-config-form');
    if (await aiConfigContent.count() > 0) {
      await expect(aiConfigContent.first()).toBeVisible();
    }
  });

  test('应该能够添加AI配置', async ({ page }) => {
    const addButton = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add');
    
    if (await addButton.count() > 0) {
      await addButton.first().click();
      await page.waitForTimeout(500);
      
      // 填写AI配置信息
      const providerInput = page.locator('input[name="provider"], select[name="provider"]');
      if (await providerInput.count() > 0) {
        await providerInput.first().fill('测试供应商');
      }
      
      const modelInput = page.locator('input[name="model_name"], input[name="modelName"]');
      if (await modelInput.count() > 0) {
        await modelInput.first().fill('test-model-v1');
      }
      
      const apiKeyInput = page.locator('input[name="api_key"], input[name="apiKey"]');
      if (await apiKeyInput.count() > 0) {
        await apiKeyInput.first().fill('test-api-key-123456');
      }
      
      // 提交表单
      const submitButton = page.locator('button[type="submit"], button:has-text("确定"), button:has-text("保存")');
      if (await submitButton.count() > 0) {
        await submitButton.first().click();
        await page.waitForTimeout(1000);
      }
    }
  });

});

test.describe('SAAS后台管理系统 - 公告管理', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到公告管理
    await page.click('a[href="/saas-admin/announcements"]');
    await page.waitForURL('**/announcements');
  });

  test('应该显示公告管理界面', async ({ page }) => {
    await expect(page.locator('h1, .page-title')).toContainText(/公告|管理/);
    
    // 检查公告列表
    const announcementList = page.locator('table, .announcement-list');
    if (await announcementList.count() > 0) {
      await expect(announcementList).toBeVisible();
    }
  });

  test('应该能够创建新公告', async ({ page }) => {
    const addButton = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add');
    
    if (await addButton.count() > 0) {
      await addButton.first().click();
      await page.waitForTimeout(500);
      
      // 填写公告信息
      const titleInput = page.locator('input[name="title"], input[placeholder*="标题"]');
      if (await titleInput.count() > 0) {
        await titleInput.first().fill('系统测试公告_' + Date.now());
      }
      
      const contentInput = page.locator('textarea[name="content"], .content-editor');
      if (await contentInput.count() > 0) {
        await contentInput.first().fill('这是一个系统测试公告，用于验证功能正常');
      }
      
      // 选择公告类型
      const typeSelect = page.locator('select[name="type"]');
      if (await typeSelect.count() > 0) {
        await typeSelect.first().selectOption('system');
      }
      
      // 提交表单
      const submitButton = page.locator('button[type="submit"], button:has-text("确定"), button:has-text("保存")');
      if (await submitButton.count() > 0) {
        await submitButton.first().click();
        await page.waitForTimeout(1000);
      }
    }
  });

});

test.describe('SAAS后台管理系统 - 知识库管理', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到知识库管理
    await page.click('a[href="/saas-admin/knowledge"]');
    await page.waitForURL('**/knowledge');
  });

  test('应该显示知识库管理界面', async ({ page }) => {
    await expect(page.locator('h1, .page-title')).toContainText(/知识库|知识|管理/);
    
    // 检查知识库列表
    const knowledgeList = page.locator('table, .knowledge-list');
    if (await knowledgeList.count() > 0) {
      await expect(knowledgeList).toBeVisible();
    }
  });

  test('应该能够创建知识库条目', async ({ page }) => {
    const addButton = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add');
    
    if (await addButton.count() > 0) {
      await addButton.first().click();
      await page.waitForTimeout(500);
      
      // 填写知识库信息
      const titleInput = page.locator('input[name="title"], input[placeholder*="标题"]');
      if (await titleInput.count() > 0) {
        await titleInput.first().fill('测试知识条目_' + Date.now());
      }
      
      const categoryInput = page.locator('input[name="category"], select[name="category"]');
      if (await categoryInput.count() > 0) {
        if (await categoryInput.first().getAttribute('type') === 'text') {
          await categoryInput.first().fill('系统测试');
        } else {
          // 如果是下拉选择，尝试选择第一个选项
          const options = page.locator('select[name="category"] option');
          if (await options.count() > 1) {
            await categoryInput.first().selectOption({ index: 1 });
          }
        }
      }
      
      const contentInput = page.locator('textarea[name="content"], .content-editor');
      if (await contentInput.count() > 0) {
        await contentInput.first().fill('这是一个测试知识条目，用于验证知识库功能是否正常工作');
      }
      
      // 提交表单
      const submitButton = page.locator('button[type="submit"], button:has-text("确定"), button:has-text("保存")');
      if (await submitButton.count() > 0) {
        await submitButton.first().click();
        await page.waitForTimeout(1000);
      }
    }
  });

});

test.describe('SAAS后台管理系统 - 系统日志', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到系统日志
    await page.click('a[href="/saas-admin/logs"]');
    await page.waitForURL('**/logs');
  });

  test('应该显示系统日志界面', async ({ page }) => {
    await expect(page.locator('h1, .page-title')).toContainText(/日志|系统|操作/);
    
    // 检查日志列表
    const logList = page.locator('table, .log-list');
    if (await logList.count() > 0) {
      await expect(logList).toBeVisible();
    }
  });

  test('应该能够筛选日志', async ({ page }) => {
    // 查找筛选器
    const filterSelect = page.locator('select[name="type"], select[name="module"]');
    
    if (await filterSelect.count() > 0) {
      // 选择一个筛选选项
      const options = page.locator('select option');
      if (await options.count() > 1) {
        await filterSelect.first().selectOption({ index: 1 });
        
        // 等待筛选结果
        await page.waitForTimeout(1000);
      }
    }
    
    // 查找搜索框
    const searchInput = page.locator('input[placeholder*="搜索"], input[type="search"]');
    if (await searchInput.count() > 0) {
      await searchInput.first().fill('login');
      await page.keyboard.press('Enter');
      
      // 等待搜索结果
      await page.waitForTimeout(1000);
    }
  });

});

test.describe('SAAS后台管理系统 - 系统监控', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到系统监控
    await page.click('a[href="/saas-admin/monitoring"]');
    await page.waitForURL('**/monitoring');
  });

  test('应该显示系统监控界面', async ({ page }) => {
    await expect(page.locator('h1, .page-title')).toContainText(/监控|性能|系统/);
    
    // 检查监控面板
    const monitoringPanel = page.locator('.monitoring-panel, .stats-panel, .chart-container');
    if (await monitoringPanel.count() > 0) {
      await expect(monitoringPanel.first()).toBeVisible();
    }
  });

});

test.describe('SAAS后台管理系统 - 退出登录', () => {
  
  test('应该能够成功退出登录', async ({ page }) => {
    // 先登录
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 点击退出登录按钮
    await page.click('a[href="/saas-admin/logout"], button:has-text("退出")');
    
    // 验证重定向到登录页面
    await page.waitForURL(/.*login/);
    await expect(page.locator('h2, .login-title')).toContainText('登录');
  });

});