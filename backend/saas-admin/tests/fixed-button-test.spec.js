const { test, expect } = require('@playwright/test');

// 测试数据
const testAdmin = {
  username: 'admin',
  password: 'admin123'
};

test.describe('修复后的按钮功能测试', () => {

  test('测试修复后的新增用户按钮', async ({ page }) => {
    console.log('=== 测试修复后的新增用户功能 ===');
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    if (await page.locator('#loginForm').count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(2000);
    }
    
    // 进入用户管理页面
    await page.goto('/saas-admin/users');
    await page.waitForTimeout(3000);
    
    console.log('当前页面URL:', page.url());
    
    // 测试新增用户按钮
    const addUserBtn = page.locator('button:has-text("新增用户")');
    console.log('新增用户按钮是否存在:', await addUserBtn.count() > 0);
    
    if (await addUserBtn.count() > 0) {
      console.log('点击新增用户按钮...');
      await addUserBtn.click();
      await page.waitForTimeout(2000);
      
      // 检查模态框是否显示
      const modal = page.locator('#addUserModal');
      const isModalVisible = await modal.isVisible();
      console.log('模态框是否显示:', isModalVisible);
      
      if (isModalVisible) {
        console.log('✅ 模态框显示成功！');
        
        // 检查表单字段
        const usernameField = modal.locator('input[name="username"]');
        const phoneField = modal.locator('input[name="phone"]');
        const tenantSelect = modal.locator('select[name="tenant_id"]');
        
        console.log('用户名字段存在:', await usernameField.count() > 0);
        console.log('手机号字段存在:', await phoneField.count() > 0);
        console.log('租户选择存在:', await tenantSelect.count() > 0);
        
        // 测试表单填写
        if (await usernameField.count() > 0) {
          await usernameField.fill('testuser123');
          console.log('填写用户名: testuser123');
        }
        
        if (await phoneField.count() > 0) {
          await phoneField.fill('13888888888');
          console.log('填写手机号: 13888888888');
        }
        
        // 关闭模态框
        const closeBtn = modal.locator('.btn-close');
        if (await closeBtn.count() > 0) {
          await closeBtn.click();
          await page.waitForTimeout(1000);
          console.log('模态框已关闭');
        }
      } else {
        console.log('❌ 模态框显示失败');
      }
    }
  });

  test('测试用户状态切换功能', async ({ page }) => {
    console.log('=== 测试用户状态切换功能 ===');
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    if (await page.locator('#loginForm').count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(2000);
    }
    
    // 进入用户管理页面
    await page.goto('/saas-admin/users');
    await page.waitForTimeout(5000); // 等待数据加载
    
    // 检查是否有用户数据
    const tableRows = page.locator('#usersTableBody tr');
    const rowCount = await tableRows.count();
    console.log(`用户列表中有 ${rowCount} 行数据`);
    
    if (rowCount > 0) {
      // 查找状态切换按钮
      const statusButtons = page.locator('button[onclick*="toggleUserStatus"]');
      const buttonCount = await statusButtons.count();
      console.log(`发现 ${buttonCount} 个状态切换按钮`);
      
      if (buttonCount > 0) {
        console.log('✅ 状态切换按钮存在');
        
        // 检查按钮是否包含正确的onclick属性
        const firstButton = statusButtons.first();
        const onclickAttr = await firstButton.getAttribute('onclick');
        console.log('第一个状态按钮的onclick属性:', onclickAttr);
        
        if (onclickAttr && onclickAttr.includes('toggleUserStatus')) {
          console.log('✅ 状态切换功能已实现');
        }
      } else {
        console.log('❌ 没有发现状态切换按钮');
      }
    } else {
      console.log('⚠️ 用户列表为空，无法测试状态切换功能');
    }
  });

  test('测试导出功能', async ({ page }) => {
    console.log('=== 测试用户导出功能 ===');
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    if (await page.locator('#loginForm').count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(2000);
    }
    
    // 进入用户管理页面
    await page.goto('/saas-admin/users');
    await page.waitForTimeout(3000);
    
    // 测试导出按钮
    const exportBtn = page.locator('button:has-text("导出用户")');
    console.log('导出用户按钮是否存在:', await exportBtn.count() > 0);
    
    if (await exportBtn.count() > 0) {
      const onclickAttr = await exportBtn.getAttribute('onclick');
      console.log('导出按钮的onclick属性:', onclickAttr);
      
      if (onclickAttr && onclickAttr.includes('exportUsers')) {
        console.log('✅ 导出功能已实现');
      }
    }
  });

  test('测试API数据加载', async ({ page }) => {
    console.log('=== 测试API数据加载情况 ===');
    
    // 监听网络请求
    const apiRequests = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        apiRequests.push(`${request.method()} ${request.url()}`);
      }
    });
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    if (await page.locator('#loginForm').count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(2000);
    }
    
    // 进入用户管理页面
    await page.goto('/saas-admin/users');
    await page.waitForTimeout(5000); // 等待API调用
    
    console.log(`捕获到 ${apiRequests.length} 个API请求:`);
    apiRequests.forEach(req => console.log(`  - ${req}`));
    
    if (apiRequests.length > 0) {
      console.log('✅ 页面正在调用API加载数据');
    } else {
      console.log('❌ 页面没有调用任何API');
    }
    
    // 检查统计数据是否更新
    const totalUsersCount = page.locator('#totalUsersCount');
    const countText = await totalUsersCount.textContent();
    console.log('总用户数显示:', countText);
    
    if (countText && countText !== '-') {
      console.log('✅ 统计数据已更新');
    } else {
      console.log('❌ 统计数据未更新');
    }
  });

});