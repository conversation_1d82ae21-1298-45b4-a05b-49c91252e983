const { test, expect } = require('@playwright/test');

// 测试数据
const testAdmin = {
  username: 'admin',
  password: 'admin123'
};

test.describe('后台管理中心 - 按钮交互功能深度测试', () => {

  test('深度测试用户管理页面的按钮交互', async ({ page }) => {
    console.log('=== 开始测试用户管理页面按钮交互 ===');
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(3000);
    }
    
    // 进入用户管理页面
    await page.goto('/saas-admin/users');
    await page.waitForTimeout(3000);
    
    console.log('当前页面URL:', page.url());
    console.log('页面标题:', await page.title());
    
    // 检查页面内容
    const pageContent = await page.content();
    console.log('页面是否包含用户管理相关内容:', pageContent.includes('用户管理'));
    
    // 查找所有按钮
    const allButtons = await page.locator('button, .btn, a[role="button"], input[type="button"], input[type="submit"]').all();
    console.log(`发现 ${allButtons.length} 个可能的按钮元素`);
    
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      try {
        const buttonText = await button.textContent() || '';
        const buttonClass = await button.getAttribute('class') || '';
        const buttonType = await button.getAttribute('type') || '';
        const buttonOnClick = await button.getAttribute('onclick') || '';
        
        console.log(`按钮 ${i+1}:`);
        console.log(`  - 文本: "${buttonText.trim()}"`);
        console.log(`  - 类名: "${buttonClass}"`);
        console.log(`  - 类型: "${buttonType}"`);
        console.log(`  - onclick: "${buttonOnClick}"`);
        
        if (buttonText.includes('新增') || buttonText.includes('添加') || buttonText.includes('创建')) {
          console.log(`  ⭐ 发现关键按钮: "${buttonText.trim()}"`);
          
          // 检查按钮是否可见和可用
          const isVisible = await button.isVisible();
          const isEnabled = await button.isEnabled();
          console.log(`  - 可见: ${isVisible}, 可用: ${isEnabled}`);
          
          if (isVisible && isEnabled) {
            // 尝试点击按钮
            console.log(`  - 尝试点击按钮...`);
            await button.click();
            await page.waitForTimeout(2000);
            
            // 检查点击后的变化
            const currentUrl = page.url();
            console.log(`  - 点击后URL: ${currentUrl}`);
            
            // 检查是否出现模态框
            const modals = await page.locator('.modal, .dialog, .popup, [role="dialog"]').all();
            console.log(`  - 发现模态框数量: ${modals.length}`);
            
            if (modals.length > 0) {
              for (let j = 0; j < modals.length; j++) {
                const modal = modals[j];
                const isModalVisible = await modal.isVisible();
                console.log(`  - 模态框 ${j+1} 可见: ${isModalVisible}`);
                
                if (isModalVisible) {
                  const modalContent = await modal.textContent();
                  console.log(`  - 模态框内容预览: "${modalContent.substring(0, 100)}..."`);
                  
                  // 查找表单字段
                  const formFields = await modal.locator('input, textarea, select').all();
                  console.log(`  - 模态框中发现 ${formFields.length} 个表单字段`);
                  
                  // 尝试关闭模态框
                  const closeButtons = await modal.locator('button:has-text("关闭"), button:has-text("取消"), .btn-close, [aria-label="Close"]').all();
                  if (closeButtons.length > 0) {
                    await closeButtons[0].click();
                    await page.waitForTimeout(1000);
                    console.log(`  - 模态框已关闭`);
                  }
                }
              }
            }
            
            // 检查是否有JavaScript错误
            const jsErrors = [];
            page.on('pageerror', error => {
              jsErrors.push(error.message);
            });
            
            if (jsErrors.length > 0) {
              console.log(`  - JavaScript错误: ${jsErrors.join(', ')}`);
            }
          }
        }
        
      } catch (error) {
        console.log(`  - 按钮测试异常: ${error.message}`);
      }
    }
    
    // 检查是否有AJAX调用
    const networkRequests = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        networkRequests.push(`${request.method()} ${request.url()}`);
      }
    });
    
    console.log(`网络请求记录: ${networkRequests.length} 个API调用`);
    networkRequests.forEach(req => console.log(`  - ${req}`));
  });

  test('测试租户管理页面的新增功能', async ({ page }) => {
    console.log('=== 开始测试租户管理页面 ===');
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(2000);
    }
    
    // 进入租户管理页面
    await page.goto('/saas-admin/tenants');
    await page.waitForTimeout(3000);
    
    console.log('租户管理页面URL:', page.url());
    
    // 检查页面源码
    const pageSource = await page.content();
    console.log('页面长度:', pageSource.length);
    console.log('页面是否包含JavaScript:', pageSource.includes('<script>'));
    console.log('页面是否包含表单:', pageSource.includes('<form>'));
    
    // 查找所有可能的新增按钮
    const addButtons = await page.locator('button:has-text("新增"), button:has-text("添加"), button:has-text("创建"), .btn-add, .add-btn, #addBtn').all();
    console.log(`发现 ${addButtons.length} 个新增相关按钮`);
    
    if (addButtons.length === 0) {
      console.log('❌ 没有发现任何新增按钮！');
      
      // 查看页面中的所有按钮
      const allButtons = await page.locator('button').all();
      console.log(`页面中总共有 ${allButtons.length} 个button元素`);
      
      for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
        const btn = allButtons[i];
        const text = await btn.textContent();
        const classes = await btn.getAttribute('class');
        console.log(`  按钮 ${i+1}: "${text}" (${classes})`);
      }
    }
    
    // 检查是否有数据表格
    const tables = await page.locator('table').all();
    console.log(`发现 ${tables.length} 个数据表格`);
    
    if (tables.length > 0) {
      for (let i = 0; i < tables.length; i++) {
        const table = tables[i];
        const rows = await table.locator('tr').all();
        console.log(`  表格 ${i+1}: ${rows.length} 行数据`);
      }
    } else {
      console.log('❌ 没有发现数据表格！');
    }
    
    // 检查是否有API数据加载
    let apiCalled = false;
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        apiCalled = true;
        console.log(`API调用: ${response.request().method()} ${response.url()} - 状态: ${response.status()}`);
      }
    });
    
    // 等待可能的AJAX加载
    await page.waitForTimeout(3000);
    
    if (!apiCalled) {
      console.log('❌ 页面没有调用任何API来加载数据！');
    }
  });

  test('测试仪表盘页面的交互元素', async ({ page }) => {
    console.log('=== 开始测试仪表盘页面 ===');
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(2000);
    }
    
    // 检查仪表盘
    await page.goto('/dashboard');
    await page.waitForTimeout(3000);
    
    console.log('仪表盘页面URL:', page.url());
    
    // 检查是否有统计数据显示
    const statsElements = await page.locator('.stat, .statistics, .metric, .card-body, .dashboard-stat').all();
    console.log(`发现 ${statsElements.length} 个统计数据元素`);
    
    // 检查数字统计
    const numbers = await page.locator('text=/\\d+/').all();
    console.log(`页面中的数字元素: ${numbers.length} 个`);
    
    // 检查是否有图表
    const charts = await page.locator('canvas, .chart, #chart, .echarts').all();
    console.log(`发现 ${charts.length} 个图表元素`);
    
    // 检查JavaScript控制台错误
    const consoleMessages = [];
    page.on('console', message => {
      if (message.type() === 'error') {
        consoleMessages.push(message.text());
      }
    });
    
    await page.waitForTimeout(2000);
    
    if (consoleMessages.length > 0) {
      console.log('JavaScript控制台错误:');
      consoleMessages.forEach(msg => console.log(`  - ${msg}`));
    } else {
      console.log('✅ 没有JavaScript错误');
    }
    
    // 检查页面是否完全加载
    const readyState = await page.evaluate(() => document.readyState);
    console.log(`页面加载状态: ${readyState}`);
    
    // 检查是否有异步数据加载
    const scriptTags = await page.locator('script').all();
    console.log(`页面中有 ${scriptTags.length} 个script标签`);
    
    for (let i = 0; i < Math.min(scriptTags.length, 5); i++) {
      const script = scriptTags[i];
      const src = await script.getAttribute('src');
      if (src) {
        console.log(`  外部脚本: ${src}`);
      } else {
        const content = await script.textContent();
        if (content && content.length > 50) {
          console.log(`  内联脚本预览: ${content.substring(0, 100)}...`);
        }
      }
    }
  });

  test('检查所有页面的实际功能实现情况', async ({ page }) => {
    console.log('=== 开始全面检查页面功能实现 ===');
    
    // 登录
    await page.goto('/');
    await page.waitForTimeout(1000);
    
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(2000);
    }
    
    // 测试所有主要页面
    const pagesToTest = [
      { name: '仪表盘', path: '/dashboard', expectedElements: ['统计', '图表', '数据'] },
      { name: '租户管理', path: '/saas-admin/tenants', expectedElements: ['新增', '表格', '搜索'] },
      { name: '用户管理', path: '/saas-admin/users', expectedElements: ['新增用户', '用户列表', '搜索'] },
      { name: '订阅计划', path: '/saas-admin/plans', expectedElements: ['新增计划', '计划列表'] },
      { name: 'AI配置', path: '/saas-admin/ai-config', expectedElements: ['配置', '设置'] },
      { name: '知识库', path: '/saas-admin/knowledge', expectedElements: ['新增文档', '文档列表'] },
      { name: '公告管理', path: '/saas-admin/announcements', expectedElements: ['发布公告', '公告列表'] },
      { name: '系统日志', path: '/saas-admin/logs', expectedElements: ['日志列表', '筛选'] }
    ];
    
    const functionReport = [];
    
    for (const pageInfo of pagesToTest) {
      console.log(`\n--- 测试 ${pageInfo.name} ---`);
      
      try {
        await page.goto(pageInfo.path);
        await page.waitForTimeout(2000);
        
        const report = {
          pageName: pageInfo.name,
          path: pageInfo.path,
          loaded: true,
          hasButtons: false,
          hasForms: false,
          hasModals: false,
          hasAjax: false,
          hasData: false,
          errors: []
        };
        
        // 检查页面是否正确加载
        const currentUrl = page.url();
        if (!currentUrl.includes(pageInfo.path.split('/').pop())) {
          report.loaded = false;
          report.errors.push('页面未正确加载');
        }
        
        // 检查按钮
        const buttons = await page.locator('button, .btn').all();
        report.hasButtons = buttons.length > 0;
        console.log(`  按钮数量: ${buttons.length}`);
        
        // 检查表单
        const forms = await page.locator('form, input, textarea').all();
        report.hasForms = forms.length > 0;
        console.log(`  表单元素: ${forms.length}`);
        
        // 检查模态框相关
        const modalTriggers = await page.locator('[data-bs-toggle="modal"], [data-toggle="modal"]').all();
        report.hasModals = modalTriggers.length > 0;
        console.log(`  模态框触发器: ${modalTriggers.length}`);
        
        // 检查数据表格
        const tables = await page.locator('table').all();
        const dataElements = await page.locator('.list-group, .data-list, tbody tr').all();
        report.hasData = tables.length > 0 || dataElements.length > 1;
        console.log(`  数据展示: 表格${tables.length}个, 数据元素${dataElements.length}个`);
        
        // 监听网络请求
        let hasApiCall = false;
        page.on('request', request => {
          if (request.url().includes('/api/')) {
            hasApiCall = true;
          }
        });
        
        await page.waitForTimeout(1000);
        report.hasAjax = hasApiCall;
        
        // 检查页面内容是否完整
        const pageContent = await page.content();
        const hasErrorPlaceholder = pageContent.includes('功能开发中') || 
                                   pageContent.includes('敬请期待') || 
                                   pageContent.includes('TODO');
        
        if (hasErrorPlaceholder) {
          report.errors.push('页面显示功能未完成');
        }
        
        // 评分
        let score = 0;
        if (report.loaded) score += 2;
        if (report.hasButtons) score += 2;
        if (report.hasForms) score += 2;
        if (report.hasModals) score += 1;
        if (report.hasAjax) score += 2;
        if (report.hasData) score += 1;
        
        report.score = score;
        report.maxScore = 10;
        
        console.log(`  功能完成度评分: ${score}/${report.maxScore} (${Math.round(score/report.maxScore*100)}%)`);
        
        functionReport.push(report);
        
      } catch (error) {
        console.log(`  页面测试异常: ${error.message}`);
        functionReport.push({
          pageName: pageInfo.name,
          path: pageInfo.path,
          loaded: false,
          errors: [error.message],
          score: 0,
          maxScore: 10
        });
      }
    }
    
    // 生成总结报告
    console.log('\n=== 功能实现情况总结 ===');
    
    let totalScore = 0;
    let maxTotalScore = 0;
    
    functionReport.forEach(report => {
      totalScore += report.score;
      maxTotalScore += report.maxScore;
      
      const percentage = Math.round((report.score / report.maxScore) * 100);
      const status = percentage >= 80 ? '✅' : percentage >= 50 ? '⚠️' : '❌';
      
      console.log(`${status} ${report.pageName}: ${report.score}/${report.maxScore} (${percentage}%)`);
      
      if (report.errors.length > 0) {
        report.errors.forEach(error => console.log(`    - ❌ ${error}`));
      }
      
      // 详细功能检查结果
      console.log(`    - 页面加载: ${report.loaded ? '✅' : '❌'}`);
      console.log(`    - 按钮交互: ${report.hasButtons ? '✅' : '❌'}`);
      console.log(`    - 表单功能: ${report.hasForms ? '✅' : '❌'}`);
      console.log(`    - 模态框: ${report.hasModals ? '✅' : '❌'}`);
      console.log(`    - API调用: ${report.hasAjax ? '✅' : '❌'}`);
      console.log(`    - 数据展示: ${report.hasData ? '✅' : '❌'}`);
    });
    
    const overallPercentage = Math.round((totalScore / maxTotalScore) * 100);
    console.log(`\n🎯 整体功能完成度: ${totalScore}/${maxTotalScore} (${overallPercentage}%)`);
    
    if (overallPercentage < 50) {
      console.log('❌ 系统功能严重不完整，多数按钮无实际功能');
    } else if (overallPercentage < 80) {
      console.log('⚠️ 系统功能部分完整，仍有较多功能缺失');
    } else {
      console.log('✅ 系统功能基本完整');
    }
    
    // 将报告保存到global，供其他测试或外部访问
    global.functionReport = functionReport;
  });

});