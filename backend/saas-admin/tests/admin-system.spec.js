const { test, expect } = require('@playwright/test');

// 测试数据
const testAdmin = {
  username: 'admin',
  password: 'admin123'
};

const testUser = {
  username: 'testuser_' + Date.now(),
  nickname: '测试用户',
  phone: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
  email: 'test' + Date.now() + '@test.com',
  tenantId: 1
};

const testPlan = {
  name: '测试订阅计划_' + Date.now(),
  code: 'test_plan_' + Date.now(),
  description: '这是一个测试订阅计划',
  priceMonthly: 99.99,
  priceYearly: 999.99,
  features: ['基础功能', '测试功能'],
  userLimit: 10
};

test.describe('SAAS后台管理系统 - 登录和认证', () => {
  
  test('应该能够访问登录页面', async ({ page }) => {
    await page.goto('/');
    
    // 检查是否重定向到登录页面
    await expect(page).toHaveURL(/.*login/);
    
    // 检查登录页面元素
    await expect(page.locator('h2')).toContainText('登录');
    await expect(page.locator('input[name="username"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('应该能够成功登录', async ({ page }) => {
    await page.goto('/');
    
    // 填写登录信息
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待跳转到仪表板
    await page.waitForURL('**/dashboard');
    
    // 检查仪表板页面元素
    await expect(page.locator('h1')).toContainText('仪表板');
    await expect(page.locator('.sidebar')).toBeVisible();
  });

  test('登录失败应该显示错误信息', async ({ page }) => {
    await page.goto('/saas-admin/login');
    
    // 使用错误的凭证
    await page.fill('input[name="username"]', 'wronguser');
    await page.fill('input[name="password"]', 'wrongpassword');
    
    await page.click('button[type="submit"]');
    
    // 检查错误信息
    await expect(page.locator('.alert, .error-message')).toBeVisible();
  });

});

test.describe('SAAS后台管理系统 - 侧边栏导航', () => {
  
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  test('应该显示完整的侧边栏导航菜单', async ({ page }) => {
    // 检查侧边栏是否可见
    await expect(page.locator('.sidebar')).toBeVisible();
    
    // 检查核心管理菜单项
    await expect(page.locator('a[href="/saas-admin/dashboard"]')).toBeVisible();
    await expect(page.locator('a[href="/saas-admin/tenants"]')).toBeVisible();
    await expect(page.locator('a[href="/saas-admin/cross-tenant-data"]')).toBeVisible();
    await expect(page.locator('a[href="/saas-admin/users"]')).toBeVisible();
    
    // 检查订阅管理菜单项
    await expect(page.locator('a[href="/saas-admin/plans"]')).toBeVisible();
    await expect(page.locator('a[href="/saas-admin/pricing"]')).toBeVisible();
    
    // 检查系统配置菜单项
    await expect(page.locator('a[href="/saas-admin/ai-config"]')).toBeVisible();
    await expect(page.locator('a[href="/saas-admin/knowledge"]')).toBeVisible();
    await expect(page.locator('a[href="/saas-admin/announcements"]')).toBeVisible();
    
    // 检查系统监控菜单项
    await expect(page.locator('a[href="/saas-admin/monitoring"]')).toBeVisible();
    await expect(page.locator('a[href="/saas-admin/logs"]')).toBeVisible();
  });

  test('应该能够在不同页面间导航', async ({ page }) => {
    // 点击租户管理
    await page.click('a[href="/saas-admin/tenants"]');
    await page.waitForURL('**/tenants');
    await expect(page.locator('h1, .page-title')).toContainText(/租户|管理/);
    
    // 点击用户管理
    await page.click('a[href="/saas-admin/users"]');
    await page.waitForURL('**/users');
    await expect(page.locator('h1, .page-title')).toContainText(/用户|管理/);
    
    // 点击订阅计划
    await page.click('a[href="/saas-admin/plans"]');
    await page.waitForURL('**/plans');
    await expect(page.locator('h1, .page-title')).toContainText(/订阅|计划/);
  });

});

test.describe('SAAS后台管理系统 - 仪表板功能', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  test('应该显示仪表板统计数据', async ({ page }) => {
    // 检查统计卡片
    const statsCards = page.locator('.card, .stat-card');
    await expect(statsCards).toHaveCount({ min: 3 });
    
    // 检查是否有租户统计
    await expect(page.locator(':text("租户")')).toBeVisible();
    
    // 检查是否有用户统计
    await expect(page.locator(':text("用户")')).toBeVisible();
  });

  test('应该能够刷新仪表板数据', async ({ page }) => {
    // 查找刷新按钮（如果存在）
    const refreshButton = page.locator('button:has-text("刷新"), .refresh-btn, [data-refresh]');
    
    if (await refreshButton.count() > 0) {
      await refreshButton.first().click();
      
      // 等待数据加载
      await page.waitForTimeout(1000);
    }
    
    // 验证页面仍然正常显示
    await expect(page.locator('h1, .page-title')).toContainText('仪表板');
  });

});

test.describe('SAAS后台管理系统 - 租户管理', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/saas-admin/login');
    await page.fill('input[name="username"]', testAdmin.username);
    await page.fill('input[name="password"]', testAdmin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 导航到租户管理
    await page.click('a[href="/saas-admin/tenants"]');
    await page.waitForURL('**/tenants');
  });

  test('应该显示租户列表', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h1, .page-title')).toContainText(/租户|管理/);
    
    // 检查表格或列表存在
    const table = page.locator('table, .tenant-list');
    if (await table.count() > 0) {
      await expect(table).toBeVisible();
    }
    
    // 检查添加按钮
    const addButton = page.locator('button:has-text("添加"), .btn-add, button:has-text("新增")');
    if (await addButton.count() > 0) {
      await expect(addButton.first()).toBeVisible();
    }
  });

  test('应该能够搜索和筛选租户', async ({ page }) => {
    // 查找搜索框
    const searchInput = page.locator('input[placeholder*="搜索"], input[type="search"], .search-input');
    
    if (await searchInput.count() > 0) {
      await searchInput.first().fill('测试');
      await page.keyboard.press('Enter');
      
      // 等待搜索结果
      await page.waitForTimeout(1000);
    }
  });

});