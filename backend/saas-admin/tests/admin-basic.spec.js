const { test, expect } = require('@playwright/test');

// 测试数据
const testAdmin = {
  username: 'admin',
  password: 'admin123'
};

test.describe('SAAS后台管理系统 - 登录和基础功能测试', () => {
  
  test('应该能够访问登录页面并进行登录', async ({ page }) => {
    // 访问登录页面
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForTimeout(2000);
    
    // 检查登录页面元素（使用 id 选择器，因为登录页面有明确的 id）
    await expect(page.locator('#username')).toBeVisible();
    await expect(page.locator('#password')).toBeVisible();
    await expect(page.locator('#loginBtn')).toBeVisible();
    
    // 检查页面标题
    await expect(page.locator('.login-title')).toContainText('平台管理登录');
    
    // 填写登录信息
    await page.fill('#username', testAdmin.username);
    await page.fill('#password', testAdmin.password);
    
    // 点击登录按钮
    await page.click('#loginBtn');
    
    // 等待登录处理
    await page.waitForTimeout(3000);
    
    // 检查是否成功跳转（可能跳转到 /dashboard 或包含 dashboard 的路径）
    const currentUrl = page.url();
    console.log('当前页面URL:', currentUrl);
    
    // 检查是否显示了仪表板相关内容
    const dashboardElement = page.locator('h1, .page-title, .login-title');
    if (await dashboardElement.count() > 0) {
      const titleText = await dashboardElement.first().textContent();
      console.log('页面标题:', titleText);
    }
  });

  test('应该能够测试所有主要页面的访问', async ({ page }) => {
    // 先登录
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // 检查是否已经登录或需要登录
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(3000);
    }
    
    // 测试主要页面路径
    const pagesToTest = [
      { name: '仪表板', path: '/dashboard' },
      { name: '仪表板-saas', path: '/saas-admin/dashboard' },
      { name: '租户管理', path: '/saas-admin/tenants' },
      { name: '用户管理', path: '/saas-admin/users' },
      { name: '订阅计划', path: '/saas-admin/plans' },
      { name: 'AI配置', path: '/saas-admin/ai-config' },
      { name: '知识库', path: '/saas-admin/knowledge' },
      { name: '公告管理', path: '/saas-admin/announcements' },
      { name: '系统日志', path: '/saas-admin/logs' }
    ];

    for (const pageInfo of pagesToTest) {
      console.log(`测试页面: ${pageInfo.name} - ${pageInfo.path}`);
      
      try {
        await page.goto(pageInfo.path);
        await page.waitForTimeout(2000);
        
        // 检查页面是否加载成功（状态码不是 404）
        const title = await page.title();
        console.log(`- ${pageInfo.name} 页面标题: ${title}`);
        
        // 检查页面是否有基本内容
        const body = page.locator('body');
        const hasContent = await body.count() > 0;
        console.log(`- ${pageInfo.name} 页面内容加载: ${hasContent ? '正常' : '异常'}`);
        
        // 查找任何可见的按钮
        const buttons = page.locator('button:visible, .btn:visible');
        const buttonCount = await buttons.count();
        console.log(`- ${pageInfo.name} 可见按钮数量: ${buttonCount}`);
        
        // 查找任何可见的输入框
        const inputs = page.locator('input:visible, textarea:visible, select:visible');
        const inputCount = await inputs.count();
        console.log(`- ${pageInfo.name} 可见输入框数量: ${inputCount}`);
        
        // 查找任何可见的链接
        const links = page.locator('a:visible');
        const linkCount = await links.count();
        console.log(`- ${pageInfo.name} 可见链接数量: ${linkCount}`);
        
        // 查找表格
        const tables = page.locator('table');
        const tableCount = await tables.count();
        if (tableCount > 0) {
          console.log(`- ${pageInfo.name} 数据表格数量: ${tableCount}`);
        }
        
      } catch (error) {
        console.log(`- ${pageInfo.name} 页面访问异常: ${error.message}`);
      }
    }
  });

  test('应该能够测试表单交互功能', async ({ page }) => {
    // 登录
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(3000);
    }
    
    // 测试具有表单的页面
    const formPages = [
      { name: '用户管理', path: '/saas-admin/users' },
      { name: '订阅计划', path: '/saas-admin/plans' },
      { name: '公告管理', path: '/saas-admin/announcements' },
      { name: 'AI配置', path: '/saas-admin/ai-config' },
      { name: '知识库', path: '/saas-admin/knowledge' }
    ];

    for (const pageInfo of formPages) {
      console.log(`测试 ${pageInfo.name} 表单功能...`);
      
      try {
        await page.goto(pageInfo.path);
        await page.waitForTimeout(2000);
        
        // 查找添加按钮
        const addButtons = page.locator('button:has-text("添加"), button:has-text("新增"), .btn-add, button:has-text("创建")');
        const addButtonCount = await addButtons.count();
        
        if (addButtonCount > 0) {
          console.log(`- ${pageInfo.name} 发现 ${addButtonCount} 个添加按钮`);
          
          // 尝试点击第一个添加按钮
          await addButtons.first().click();
          await page.waitForTimeout(1000);
          
          // 查找出现的模态框或表单
          const modal = page.locator('.modal:visible, .dialog:visible, .popup:visible');
          const form = page.locator('form:visible');
          
          if (await modal.count() > 0) {
            console.log(`- ${pageInfo.name} 模态框打开成功`);
            
            // 查找表单字段
            const formFields = page.locator('.modal input:visible, .modal textarea:visible, .modal select:visible');
            const fieldCount = await formFields.count();
            console.log(`- ${pageInfo.name} 模态框中发现 ${fieldCount} 个表单字段`);
            
            // 尝试关闭模态框
            const closeBtn = page.locator('.modal .btn-close, .modal :text("取消"), .modal :text("关闭")');
            if (await closeBtn.count() > 0) {
              await closeBtn.first().click();
              await page.waitForTimeout(500);
              console.log(`- ${pageInfo.name} 模态框关闭成功`);
            } else {
              await page.keyboard.press('Escape');
              await page.waitForTimeout(500);
              console.log(`- ${pageInfo.name} 使用 ESC 键关闭模态框`);
            }
            
          } else if (await form.count() > 0) {
            console.log(`- ${pageInfo.name} 表单显示成功`);
            
            const formFields = page.locator('form input:visible, form textarea:visible, form select:visible');
            const fieldCount = await formFields.count();
            console.log(`- ${pageInfo.name} 表单中发现 ${fieldCount} 个字段`);
          } else {
            console.log(`- ${pageInfo.name} 点击添加按钮后无明显变化`);
          }
          
        } else {
          console.log(`- ${pageInfo.name} 未发现添加按钮`);
        }
        
        // 测试搜索功能
        const searchInputs = page.locator('input[placeholder*="搜索"], input[type="search"], .search-input');
        if (await searchInputs.count() > 0) {
          console.log(`- ${pageInfo.name} 发现搜索功能`);
          await searchInputs.first().fill('test');
          await page.keyboard.press('Enter');
          await page.waitForTimeout(1000);
          console.log(`- ${pageInfo.name} 搜索功能测试完成`);
          
          // 清空搜索
          await searchInputs.first().clear();
          await page.keyboard.press('Enter');
          await page.waitForTimeout(500);
        }
        
      } catch (error) {
        console.log(`- ${pageInfo.name} 表单测试异常: ${error.message}`);
      }
    }
  });

  test('应该能够测试所有可见按钮的交互性', async ({ page }) => {
    // 登录
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(3000);
    }
    
    // 测试主要页面的按钮交互
    const pagesToTest = [
      { name: '仪表板', path: '/saas-admin/dashboard' },
      { name: '租户管理', path: '/saas-admin/tenants' },
      { name: '用户管理', path: '/saas-admin/users' },
      { name: '订阅计划', path: '/saas-admin/plans' }
    ];

    for (const pageInfo of pagesToTest) {
      console.log(`测试 ${pageInfo.name} 页面按钮交互...`);
      
      try {
        await page.goto(pageInfo.path);
        await page.waitForTimeout(2000);
        
        // 获取所有可见按钮
        const buttons = page.locator('button:visible, .btn:visible');
        const buttonCount = await buttons.count();
        console.log(`- ${pageInfo.name} 发现 ${buttonCount} 个可见按钮`);
        
        // 测试前几个按钮（避免测试太多）
        const testLimit = Math.min(buttonCount, 5);
        for (let i = 0; i < testLimit; i++) {
          const button = buttons.nth(i);
          const buttonText = await button.textContent();
          
          // 跳过危险按钮
          if (!buttonText || 
              buttonText.includes('删除') || 
              buttonText.includes('退出') ||
              buttonText.includes('登出')) {
            continue;
          }
          
          try {
            // 检查按钮是否可用
            const isEnabled = await button.isEnabled();
            if (isEnabled) {
              // 悬停测试
              await button.hover();
              await page.waitForTimeout(200);
              console.log(`  - 按钮 "${buttonText}" 可交互`);
            } else {
              console.log(`  - 按钮 "${buttonText}" 已禁用`);
            }
          } catch (error) {
            console.log(`  - 按钮 "${buttonText}" 交互异常: ${error.message}`);
          }
        }
        
        // 测试链接
        const links = page.locator('a:visible');
        const linkCount = await links.count();
        console.log(`- ${pageInfo.name} 发现 ${linkCount} 个可见链接`);
        
      } catch (error) {
        console.log(`- ${pageInfo.name} 页面测试异常: ${error.message}`);
      }
    }
  });

  test('应该能够测试侧边栏导航功能', async ({ page }) => {
    // 登录
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const loginForm = page.locator('#loginForm');
    if (await loginForm.count() > 0) {
      await page.fill('#username', testAdmin.username);
      await page.fill('#password', testAdmin.password);
      await page.click('#loginBtn');
      await page.waitForTimeout(3000);
    }
    
    // 检查侧边栏是否存在
    const sidebar = page.locator('.sidebar');
    if (await sidebar.count() > 0) {
      console.log('发现侧边栏导航');
      
      // 测试侧边栏中的导航链接
      const navLinks = page.locator('.sidebar a:visible');
      const navLinkCount = await navLinks.count();
      console.log(`侧边栏中发现 ${navLinkCount} 个导航链接`);
      
      // 测试前几个导航链接
      const testLimit = Math.min(navLinkCount, 5);
      for (let i = 0; i < testLimit; i++) {
        const link = navLinks.nth(i);
        const linkText = await link.textContent();
        const linkHref = await link.getAttribute('href');
        
        if (!linkText || linkText.includes('退出') || linkText.includes('登出')) {
          continue;
        }
        
        try {
          console.log(`测试导航链接: "${linkText}" -> ${linkHref}`);
          
          // 点击导航链接
          await link.click();
          await page.waitForTimeout(2000);
          
          // 检查 URL 变化
          const currentUrl = page.url();
          console.log(`  导航后的 URL: ${currentUrl}`);
          
          // 检查页面内容是否加载
          const pageContent = page.locator('body');
          const hasContent = await pageContent.count() > 0;
          console.log(`  页面内容加载: ${hasContent ? '正常' : '异常'}`);
          
        } catch (error) {
          console.log(`  导航链接 "${linkText}" 测试异常: ${error.message}`);
        }
      }
    } else {
      console.log('未发现侧边栏导航');
    }
  });

});