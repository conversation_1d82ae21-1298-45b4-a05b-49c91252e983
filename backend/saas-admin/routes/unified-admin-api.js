/**
 * 统一的管理后台API路由
 * 基于Context7最佳实践，整合所有管理功能的API接口
 */

const express = require('express');
const router = express.Router();

/**
 * 统一的API响应格式
 * 基于Context7推荐的标准化响应结构
 */
const createResponse = (success, data = null, message = '', code = 200) => {
  return {
    success,
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  };
};

/**
 * 错误处理中间件
 */
const handleError = (error, req, res, next) => {
  console.error('API Error:', error);
  
  const statusCode = error.statusCode || 500;
  const message = error.message || '服务器内部错误';
  
  res.status(statusCode).json(createResponse(false, null, message, statusCode));
};

/**
 * 统计数据API - 仪表盘
 * GET /api/admin/statistics
 */
router.get('/statistics', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    
    // 并行查询各种统计数据
    const [tenantStats] = await db.query(`
      SELECT 
        COUNT(*) as total_tenants,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_tenants,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_tenants,
        COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_tenants
      FROM tenant_info
    `);
    
    const [userStats] = await db.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_new_users
      FROM users
    `);
    
    // 暂时使用模拟的收入数据，因为payments表可能不存在
    const revenueStats = [{
      total_revenue: 0,
      today_revenue: 0,
      month_revenue: 0
    }];
    
    const statistics = {
      tenants: tenantStats[0],
      users: userStats[0],
      revenue: revenueStats[0],
      pending: {
        tenant: tenantStats[0].pending_tenants,
        user: 0, // 用户申请待审批数量
        total: tenantStats[0].pending_tenants
      },
      today: {
        approved: 0, // 今日已审批数量
        rejected: 0  // 今日已拒绝数量
      }
    };
    
    res.json(createResponse(true, statistics, '统计数据获取成功'));
  } catch (error) {
    next(error);
  }
});

/**
 * 租户管理API
 */
router.get('/tenants', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { page = 1, limit = 10, status, search } = req.query;
    
    let whereClause = '1=1';
    let params = [];
    
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    if (search) {
      whereClause += ' AND (name LIKE ? OR tenant_code LIKE ? OR contact_name LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    const offset = (page - 1) * limit;
    
    const [tenants] = await db.query(`
      SELECT id, name, tenant_code, contact_name, contact_phone, contact_email,
             status, subscription_plan, created_at, updated_at
      FROM tenant_info
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM tenant_info WHERE ${whereClause}
    `, params);
    
    const total = countResult[0].total;
    
    res.json(createResponse(true, {
      list: tenants,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }, '租户列表获取成功'));
  } catch (error) {
    next(error);
  }
});

/**
 * 用户管理API
 */
router.get('/users', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { page = 1, limit = 10, tenant_id, search } = req.query;
    
    let whereClause = '1=1';
    let params = [];
    
    if (tenant_id) {
      whereClause += ' AND u.tenant_id = ?';
      params.push(tenant_id);
    }
    
    if (search) {
      whereClause += ' AND (u.username LIKE ? OR u.nickname LIKE ? OR u.phone LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    const offset = (page - 1) * limit;
    
    const [users] = await db.query(`
      SELECT u.id, u.username, u.nickname, u.phone, u.email, u.status,
             u.created_at, u.updated_at,
             t.name as tenant_name, t.tenant_code
      FROM users u
      LEFT JOIN tenant_info t ON u.tenant_id = t.id
      WHERE ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM users u WHERE ${whereClause}
    `, params);
    
    const total = countResult[0].total;
    
    res.json(createResponse(true, {
      list: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }, '用户列表获取成功'));
  } catch (error) {
    next(error);
  }
});

/**
 * 审批管理API - 待审批列表
 */
router.get('/pending', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { limit = 10 } = req.query;
    
    // 获取待审批的租户申请
    const [pendingTenants] = await db.query(`
      SELECT 'tenant' as application_type, id as approval_id, name as farm_name,
             contact_phone as tenant_phone, NULL as user_phone,
             contact_name as real_name, created_at
      FROM tenant_info
      WHERE status = 'pending'
      ORDER BY created_at DESC
      LIMIT ${parseInt(limit)}
    `);

    // 获取待审批的用户申请（如果有的话）
    const [pendingUsers] = await db.query(`
      SELECT 'user' as application_type, id as approval_id, nickname as real_name,
             phone as user_phone, NULL as tenant_phone,
             NULL as farm_name, created_at
      FROM users
      WHERE status = 'pending'
      ORDER BY created_at DESC
      LIMIT ${parseInt(limit)}
    `);
    
    const allPending = [...pendingTenants, ...pendingUsers]
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      .slice(0, limit);
    
    res.json(createResponse(true, {
      list: allPending,
      total: allPending.length
    }, '待审批列表获取成功'));
  } catch (error) {
    next(error);
  }
});

/**
 * 审批操作API
 */
router.post('/approve/:id', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { action, comment, application_type } = req.body; // action: 'approve' | 'reject'

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json(createResponse(false, null, '无效的审批操作'));
    }

    // 根据申请类型更新相应的表
    if (application_type === 'tenant') {
      const newStatus = action === 'approve' ? 'active' : 'rejected';
      await db.query(`
        UPDATE tenant_info
        SET status = ?, updated_at = NOW()
        WHERE id = ?
      `, [newStatus, id]);

      // 记录审批日志
      await db.query(`
        INSERT INTO approval_logs (application_type, application_id, action, comment, created_at)
        VALUES (?, ?, ?, ?, NOW())
      `, ['tenant', id, action, comment || '']);

    } else if (application_type === 'user') {
      const newStatus = action === 'approve' ? 'active' : 'rejected';
      await db.query(`
        UPDATE users
        SET status = ?, updated_at = NOW()
        WHERE id = ?
      `, [newStatus, id]);

      // 记录审批日志
      await db.query(`
        INSERT INTO approval_logs (application_type, application_id, action, comment, created_at)
        VALUES (?, ?, ?, ?, NOW())
      `, ['user', id, action, comment || '']);
    }

    res.json(createResponse(true, null, `审批${action === 'approve' ? '通过' : '拒绝'}成功`));
  } catch (error) {
    next(error);
  }
});

/**
 * 租户管理CRUD操作
 */

// 获取单个租户详情
router.get('/tenants/:id', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    const [tenants] = await db.query(`
      SELECT * FROM tenant_info WHERE id = ?
    `, [id]);

    if (tenants.length === 0) {
      return res.status(404).json(createResponse(false, null, '租户不存在'));
    }

    res.json(createResponse(true, tenants[0], '租户详情获取成功'));
  } catch (error) {
    next(error);
  }
});

// 创建新租户
router.post('/tenants', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { name, tenant_code, contact_name, contact_phone, contact_email, subscription_plan } = req.body;

    // 验证必填字段
    if (!name || !tenant_code || !contact_name || !contact_phone) {
      return res.status(400).json(createResponse(false, null, '缺少必填字段'));
    }

    // 检查租户代码是否已存在
    const [existing] = await db.query(`
      SELECT id FROM tenant_info WHERE tenant_code = ?
    `, [tenant_code]);

    if (existing.length > 0) {
      return res.status(400).json(createResponse(false, null, '租户代码已存在'));
    }

    // 创建新租户
    const [result] = await db.query(`
      INSERT INTO tenant_info (name, tenant_code, contact_name, contact_phone, contact_email,
                              subscription_plan, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
    `, [name, tenant_code, contact_name, contact_phone, contact_email, subscription_plan || 'basic']);

    res.json(createResponse(true, { id: result.insertId }, '租户创建成功'));
  } catch (error) {
    next(error);
  }
});

// 更新租户信息
router.put('/tenants/:id', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { name, contact_name, contact_phone, contact_email, subscription_plan, status } = req.body;

    // 检查租户是否存在
    const [existing] = await db.query(`
      SELECT id FROM tenant_info WHERE id = ?
    `, [id]);

    if (existing.length === 0) {
      return res.status(404).json(createResponse(false, null, '租户不存在'));
    }

    // 更新租户信息
    await db.query(`
      UPDATE tenant_info
      SET name = ?, contact_name = ?, contact_phone = ?, contact_email = ?,
          subscription_plan = ?, status = ?, updated_at = NOW()
      WHERE id = ?
    `, [name, contact_name, contact_phone, contact_email, subscription_plan, status, id]);

    res.json(createResponse(true, null, '租户信息更新成功'));
  } catch (error) {
    next(error);
  }
});

// 删除租户
router.delete('/tenants/:id', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 检查租户是否存在
    const [existing] = await db.query(`
      SELECT id FROM tenant_info WHERE id = ?
    `, [id]);

    if (existing.length === 0) {
      return res.status(404).json(createResponse(false, null, '租户不存在'));
    }

    // 检查是否有关联的用户
    const [users] = await db.query(`
      SELECT COUNT(*) as count FROM users WHERE tenant_id = ?
    `, [id]);

    if (users[0].count > 0) {
      return res.status(400).json(createResponse(false, null, '该租户下还有用户，无法删除'));
    }

    // 删除租户
    await db.query(`
      DELETE FROM tenant_info WHERE id = ?
    `, [id]);

    res.json(createResponse(true, null, '租户删除成功'));
  } catch (error) {
    next(error);
  }
});

/**
 * 用户管理CRUD操作
 */

// 获取单个用户详情
router.get('/users/:id', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    const [users] = await db.query(`
      SELECT u.*, t.name as tenant_name, t.tenant_code
      FROM users u
      LEFT JOIN tenant_info t ON u.tenant_id = t.id
      WHERE u.id = ?
    `, [id]);

    if (users.length === 0) {
      return res.status(404).json(createResponse(false, null, '用户不存在'));
    }

    res.json(createResponse(true, users[0], '用户详情获取成功'));
  } catch (error) {
    next(error);
  }
});

// 创建新用户
router.post('/users', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { username, nickname, phone, email, tenant_id, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json(createResponse(false, null, '用户名和密码为必填字段'));
    }

    // 检查用户名是否已存在
    const [existing] = await db.query(`
      SELECT id FROM users WHERE username = ?
    `, [username]);

    if (existing.length > 0) {
      return res.status(400).json(createResponse(false, null, '用户名已存在'));
    }

    // 创建新用户（这里应该对密码进行加密，暂时简化处理）
    const [result] = await db.query(`
      INSERT INTO users (username, nickname, phone, email, tenant_id, password, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
    `, [username, nickname, phone, email, tenant_id, password]);

    res.json(createResponse(true, { id: result.insertId }, '用户创建成功'));
  } catch (error) {
    next(error);
  }
});

// 更新用户信息
router.put('/users/:id', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;
    const { nickname, phone, email, tenant_id, status } = req.body;

    // 检查用户是否存在
    const [existing] = await db.query(`
      SELECT id FROM users WHERE id = ?
    `, [id]);

    if (existing.length === 0) {
      return res.status(404).json(createResponse(false, null, '用户不存在'));
    }

    // 更新用户信息
    await db.query(`
      UPDATE users
      SET nickname = ?, phone = ?, email = ?, tenant_id = ?, status = ?, updated_at = NOW()
      WHERE id = ?
    `, [nickname, phone, email, tenant_id, status, id]);

    res.json(createResponse(true, null, '用户信息更新成功'));
  } catch (error) {
    next(error);
  }
});

// 删除用户
router.delete('/users/:id', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { id } = req.params;

    // 检查用户是否存在
    const [existing] = await db.query(`
      SELECT id FROM users WHERE id = ?
    `, [id]);

    if (existing.length === 0) {
      return res.status(404).json(createResponse(false, null, '用户不存在'));
    }

    // 删除用户
    await db.query(`
      DELETE FROM users WHERE id = ?
    `, [id]);

    res.json(createResponse(true, null, '用户删除成功'));
  } catch (error) {
    next(error);
  }
});

/**
 * 审批历史API
 */
router.get('/approval-history', async (req, res, next) => {
  try {
    const db = req.app.locals.db;
    const { page = 1, limit = 10, application_type, action } = req.query;

    let whereClause = '1=1';
    let params = [];

    if (application_type) {
      whereClause += ' AND application_type = ?';
      params.push(application_type);
    }

    if (action) {
      whereClause += ' AND action = ?';
      params.push(action);
    }

    const offset = (page - 1) * limit;

    const [history] = await db.query(`
      SELECT * FROM approval_logs
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
    `, params);

    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM approval_logs WHERE ${whereClause}
    `, params);

    const total = countResult[0].total;

    res.json(createResponse(true, {
      list: history,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }, '审批历史获取成功'));
  } catch (error) {
    next(error);
  }
});

// 错误处理中间件
router.use(handleError);

module.exports = router;
