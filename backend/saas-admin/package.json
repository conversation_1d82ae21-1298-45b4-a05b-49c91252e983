{"name": "smart-goose-saas-admin", "version": "1.0.0", "description": "智慧养鹅SAAS平台管理后台", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report"}, "dependencies": {"bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "helmet": "^7.1.0", "mysql2": "^3.6.5"}, "devDependencies": {"@playwright/test": "^1.55.0", "nodemon": "^3.0.2"}}