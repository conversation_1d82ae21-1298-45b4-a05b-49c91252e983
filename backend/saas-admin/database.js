const mysql = require('mysql2/promise');

// SAAS管理后台数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 数据库操作封装
const db = {
  // 执行查询
  async query(sql, params = []) {
    try {
      console.log('SQL语句:', sql);
      console.log('参数:', params);
      const [results] = await pool.execute(sql, params);
      return [results];
    } catch (error) {
      console.error('SQL查询错误:', error);
      throw error;
    }
  },

  // 健康检查
  async healthCheck() {
    try {
      const [results] = await pool.execute('SELECT 1 as health');
      return {
        status: 'healthy',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  },

  // 初始化数据库
  async initDatabase() {
    try {
      console.log('🔧 开始初始化数据库...');
      
      // 测试连接
      await this.healthCheck();
      console.log('✅ 数据库连接成功');
      
      // 执行初始化SQL脚本
      const fs = require('fs');
      const path = require('path');
      const sqlPath = path.join(__dirname, 'init-admin-tables.sql');
      
      if (fs.existsSync(sqlPath)) {
        const sqlContent = fs.readFileSync(sqlPath, 'utf8');
        const statements = sqlContent.split(';').filter(stmt => stmt.trim());
        
        for (const statement of statements) {
          if (statement.trim()) {
            try {
              await pool.execute(statement);
            } catch (error) {
              // 忽略已存在的表错误
              if (!error.message.includes('already exists')) {
                console.log('SQL执行警告:', error.message);
              }
            }
          }
        }
        console.log('✅ SQL脚本执行完成');
      }
      
      console.log('✅ 数据库初始化完成');
      return true;
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error);
      return false;
    }
  },

  // 关闭连接池
  async closePool() {
    try {
      await pool.end();
      console.log('数据库连接池已关闭');
    } catch (error) {
      console.error('关闭数据库连接池失败:', error);
    }
  }
};

module.exports = db;