const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const path = require('path');
const session = require('express-session');
require('dotenv').config();

const db = require('./database');

const app = express();
const PORT = process.env.ADMIN_PORT || 4000;

// 设置数据库连接为app.locals，供路由使用
app.locals.db = db;

// 安全中间件
app.use(helmet());
app.use(compression());

// CORS配置 - 开发环境允许所有来源
const corsOptions = {
  origin: function (origin, callback) {
    // 开发环境或本地访问允许所有来源
    if (process.env.NODE_ENV === 'development' || !origin || origin.includes('localhost')) {
      return callback(null, true);
    }

    const allowedOrigins = (process.env.ADMIN_ALLOWED_ORIGINS || 'http://localhost:4000,http://localhost:4001').split(',');

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 限制每个IP 15分钟内最多200个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后重试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Session配置
app.use(session({
  secret: process.env.SESSION_SECRET || 'smart-goose-admin-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    maxAge: 8 * 60 * 60 * 1000, // 8小时
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true
  }
}));

// 静态文件服务
app.use('/static', express.static(path.join(__dirname, 'public')));

// 添加项目根目录的静态文件服务，支持Vue + Element Plus管理界面
app.use('/admin', express.static(path.join(__dirname, '../../admin')));
app.use('/components', express.static(path.join(__dirname, '../../components')));
app.use('/utils', express.static(path.join(__dirname, '../../utils')));
app.use('/styles', express.static(path.join(__dirname, '../../styles')));
app.use('/images', express.static(path.join(__dirname, '../../images')));
app.use('/constants', express.static(path.join(__dirname, '../../constants')));

// 为管理界面提供专门的路由
app.get('/admin-ui', (req, res) => {
  res.sendFile(path.join(__dirname, '../../admin/dashboard.html'));
});

app.get('/admin-ui/*', (req, res) => {
  res.sendFile(path.join(__dirname, '../../admin/dashboard.html'));
});

// 模板引擎设置
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 引入统一的管理API路由
const unifiedAdminApiRoutes = require('./routes/unified-admin-api');

// 注册统一的管理API路由
app.use('/api/admin', unifiedAdminApiRoutes);

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip}`);
  next();
});

// 基础API路由
app.get('/api/tenants', async (req, res) => {
  try {
    const [tenants] = await db.query('SELECT id, name, status, created_at FROM tenant_info ORDER BY created_at DESC');
    res.json({
      success: true,
      data: tenants
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取租户列表失败',
      error: error.message
    });
  }
});

app.get('/api/stats', async (req, res) => {
  try {
    const [tenantCount] = await db.query('SELECT COUNT(*) as count FROM tenant_info WHERE status = "active"');
    const [userCount] = await db.query('SELECT COUNT(*) as count FROM users');
    
    res.json({
      success: true,
      data: {
        totalTenants: tenantCount[0]?.count || 0,
        totalUsers: userCount[0]?.count || 0,
        systemUptime: process.uptime()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 健康检查端点
app.get('/api/health', async (req, res) => {
  try {
    const dbHealth = await db.healthCheck();
    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      service: 'Smart Goose SAAS Admin',
      version: '1.0.0',
      database: dbHealth,
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'SAAS管理后台健康检查失败',
      error: error.message
    });
  }
});

// 管理员登录页面
app.get('/', (req, res) => {
  if (req.session.admin) {
    return res.redirect('/dashboard');
  }
  try {
    res.render('auth/login', {
      title: '智慧养鹅 SAAS 管理后台',
      error: null
    });
  } catch (error) {
    res.send(`
      <h1>智慧养鹅 SAAS 管理后台</h1>
      <form method="post" action="/login">
        <input type="text" name="username" placeholder="用户名" required>
        <input type="password" name="password" placeholder="密码" required>
        <button type="submit">登录</button>
      </form>
    `);
  }
});

// 管理员登录处理 - 兼容多种路径
app.post('/login', handleLogin);
app.post('/saas-admin/login', handleLogin);

async function handleLogin(req, res) {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      // 检查是否为AJAX请求
      if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
        return res.status(400).json({
          success: false,
          message: '用户名和密码不能为空'
        });
      }
      // 表单提交重定向回登录页面显示错误
      return res.redirect('/?error=missing_fields');
    }

    // 查询SAAS管理员用户
    const [admins] = await db.query(
      'SELECT * FROM saas_platform_admins WHERE username = ? AND status = "active"',
      [username]
    );

    if (admins.length === 0) {
      if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }
      return res.redirect('/?error=invalid_credentials');
    }

    const admin = admins[0];
    
    // 验证密码
    const bcrypt = require('bcrypt');
    const isValidPassword = await bcrypt.compare(password, admin.password);
    
    if (!isValidPassword) {
      if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }
      return res.redirect('/?error=invalid_credentials');
    }

    // 设置session
    req.session.admin = {
      id: admin.id,
      username: admin.username,
      name: admin.name,
      role: admin.role
    };

    // 更新最后登录时间
    await db.query(
      'UPDATE saas_platform_admins SET last_login_time = CURRENT_TIMESTAMP WHERE id = ?',
      [admin.id]
    );

    // 记录登录日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      admin.id,
      'login',
      'auth',
      'SAAS管理员登录系统',
      req.ip,
      req.get('User-Agent')
    ]);

    // 根据请求类型返回不同响应
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.json({
        success: true,
        message: '登录成功',
        redirect: '/dashboard'
      });
    }
    
    // 表单提交直接重定向到仪表盘
    return res.redirect('/dashboard');

  } catch (error) {
    console.error('SAAS管理员登录失败:', error);
    
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.status(500).json({
        success: false,
        message: '登录失败，请重试'
      });
    }
    
    return res.redirect('/?error=system_error');
  }
}

// 仪表盘页面
app.get('/dashboard', (req, res) => {
  if (!req.session.admin) {
    return res.redirect('/');
  }
  
  try {
    res.render('dashboard/index', {
      title: '仪表盘 - SAAS管理后台',
      admin: req.session.admin,
      adminName: req.session.admin.name
    });
  } catch (error) {
    console.error('渲染仪表盘失败:', error);
    res.send(`
      <h1>SAAS管理后台仪表盘</h1>
      <p>欢迎，${req.session.admin.name}！</p>
      <p>管理功能正在加载中...</p>
      <a href="/logout">退出登录</a>
    `);
  }
});

// 认证中间件
function requireAuth(req, res, next) {
  if (!req.session.admin) {
    return res.redirect('/');
  }
  next();
}

// 核心管理页面路由
app.get('/tenants', requireAuth, (req, res) => {
  res.render('tenants/index', { title: '租户管理', admin: req.session.admin, adminName: req.session.admin.name });
});

app.get('/saas-admin/tenants', requireAuth, (req, res) => {
  res.render('tenants/index', { title: '租户管理', admin: req.session.admin, adminName: req.session.admin.name });
});

app.get('/cross-tenant-data', requireAuth, (req, res) => {
  res.render('tenants/cross-tenant-data', { title: '数据监控', admin: req.session.admin, adminName: req.session.admin.name });
});

app.get('/saas-admin/cross-tenant-data', requireAuth, (req, res) => {
  res.render('tenants/cross-tenant-data', { title: '数据监控', admin: req.session.admin, adminName: req.session.admin.name });
});

app.get('/users', requireAuth, (req, res) => {
  res.render('users/index', { title: '平台用户', admin: req.session.admin, adminName: req.session.admin.name });
});

app.get('/saas-admin/users', requireAuth, (req, res) => {
  res.render('users/index', { title: '平台用户', admin: req.session.admin, adminName: req.session.admin.name });
});

// 订阅管理页面路由
app.get('/plans', requireAuth, async (req, res) => {
  try {
    const planStats = {
      totalPlans: 4,
      activePlans: 3,
      totalSubscriptions: 156,
      monthlyRevenue: 12580
    };
    res.render('plans/index', { 
      title: '订阅计划', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      planStats
    });
  } catch (error) {
    console.error('订阅计划页面错误:', error);
    res.render('plans/index', { 
      title: '订阅计划', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      planStats: { totalPlans: 0, activePlans: 0, totalSubscriptions: 0, monthlyRevenue: 0 }
    });
  }
});

app.get('/saas-admin/plans', requireAuth, async (req, res) => {
  try {
    const planStats = {
      totalPlans: 4,
      activePlans: 3,
      totalSubscriptions: 156,
      monthlyRevenue: 12580
    };
    res.render('plans/index', { 
      title: '订阅计划', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      planStats
    });
  } catch (error) {
    console.error('订阅计划页面错误:', error);
    res.render('plans/index', { 
      title: '订阅计划', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      planStats: { totalPlans: 0, activePlans: 0, totalSubscriptions: 0, monthlyRevenue: 0 }
    });
  }
});

app.get('/pricing', requireAuth, async (req, res) => {
  try {
    const pricingStats = {
      totalPricingRules: 12,
      activePricingRules: 8,
      discountRules: 5,
      avgPrice: 89.5
    };
    res.render('pricing/index', { 
      title: '价格管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      pricingStats
    });
  } catch (error) {
    console.error('价格管理页面错误:', error);
    res.render('pricing/index', { 
      title: '价格管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      pricingStats: { totalPricingRules: 0, activePricingRules: 0, discountRules: 0, avgPrice: 0 }
    });
  }
});

app.get('/saas-admin/pricing', requireAuth, async (req, res) => {
  try {
    const pricingStats = {
      totalPricingRules: 12,
      activePricingRules: 8,
      discountRules: 5,
      avgPrice: 89.5
    };
    res.render('pricing/index', { 
      title: '价格管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      pricingStats
    });
  } catch (error) {
    console.error('价格管理页面错误:', error);
    res.render('pricing/index', { 
      title: '价格管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      pricingStats: { totalPricingRules: 0, activePricingRules: 0, discountRules: 0, avgPrice: 0 }
    });
  }
});

// 系统配置页面路由
app.get('/ai-config', requireAuth, async (req, res) => {
  try {
    const aiStats = {
      totalProviders: 5,
      activeProviders: 3,
      totalConfigs: 12,
      successRate: 98.5
    };
    res.render('ai-config/index', { 
      title: 'AI配置', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      aiStats
    });
  } catch (error) {
    console.error('AI配置页面错误:', error);
    res.render('ai-config/index', { 
      title: 'AI配置', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      aiStats: { totalProviders: 0, activeProviders: 0, totalConfigs: 0, successRate: 0 }
    });
  }
});

app.get('/saas-admin/ai-config', requireAuth, async (req, res) => {
  try {
    const aiStats = {
      totalProviders: 5,
      activeProviders: 3,
      totalConfigs: 12,
      successRate: 98.5,
      totalDailyCalls: 1234,
      totalMonthlyCalls: 45678
    };
    res.render('ai-config/index', { 
      title: 'AI配置', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      aiStats
    });
  } catch (error) {
    console.error('AI配置页面错误:', error);
    res.render('ai-config/index', { 
      title: 'AI配置', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      aiStats: { 
        totalProviders: 0, 
        activeProviders: 0, 
        totalConfigs: 0, 
        successRate: 0,
        totalDailyCalls: 0,
        totalMonthlyCalls: 0
      }
    });
  }
});

app.get('/knowledge', requireAuth, async (req, res) => {
  try {
    const knowledgeStats = {
      totalDocuments: 156,
      totalCategories: 12,
      recentUpdates: 8,
      popularQueries: 25
    };
    res.render('knowledge/index', { 
      title: '知识库', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      knowledgeStats
    });
  } catch (error) {
    console.error('知识库页面错误:', error);
    res.render('knowledge/index', { 
      title: '知识库', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      knowledgeStats: { totalDocuments: 0, totalCategories: 0, recentUpdates: 0, popularQueries: 0 }
    });
  }
});

app.get('/saas-admin/knowledge', requireAuth, async (req, res) => {
  try {
    const knowledgeStats = {
      totalDocuments: 156,
      totalCategories: 12,
      recentUpdates: 8,
      popularQueries: 25
    };
    res.render('knowledge/index', { 
      title: '知识库', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      knowledgeStats
    });
  } catch (error) {
    console.error('知识库页面错误:', error);
    res.render('knowledge/index', { 
      title: '知识库', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      knowledgeStats: { totalDocuments: 0, totalCategories: 0, recentUpdates: 0, popularQueries: 0 }
    });
  }
});

app.get('/announcements', requireAuth, async (req, res) => {
  try {
    const announcementStats = {
      totalAnnouncements: 23,
      activeAnnouncements: 8,
      scheduledAnnouncements: 3,
      totalViews: 1245
    };
    res.render('announcements/index', { 
      title: '公告管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      announcementStats
    });
  } catch (error) {
    console.error('公告管理页面错误:', error);
    res.render('announcements/index', { 
      title: '公告管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      announcementStats: { totalAnnouncements: 0, activeAnnouncements: 0, scheduledAnnouncements: 0, totalViews: 0 }
    });
  }
});

app.get('/saas-admin/announcements', requireAuth, async (req, res) => {
  try {
    const announcementStats = {
      totalAnnouncements: 23,
      activeAnnouncements: 8,
      scheduledAnnouncements: 3,
      totalViews: 1245
    };
    res.render('announcements/index', { 
      title: '公告管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      announcementStats
    });
  } catch (error) {
    console.error('公告管理页面错误:', error);
    res.render('announcements/index', { 
      title: '公告管理', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      announcementStats: { totalAnnouncements: 0, activeAnnouncements: 0, scheduledAnnouncements: 0, totalViews: 0 }
    });
  }
});

// 系统监控页面路由
app.get('/monitoring', requireAuth, async (req, res) => {
  try {
    const monitoringStats = {
      cpuUsage: 25.6,
      memoryUsage: 68.3,
      diskUsage: 42.1,
      activeConnections: 127,
      responseTime: 145,
      errorRate: 0.02
    };
    res.render('monitoring/index', { 
      title: '性能监控', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      monitoringStats
    });
  } catch (error) {
    console.error('监控页面错误:', error);
    res.render('monitoring/index', { 
      title: '性能监控', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      monitoringStats: { cpuUsage: 0, memoryUsage: 0, diskUsage: 0, activeConnections: 0, responseTime: 0, errorRate: 0 }
    });
  }
});

app.get('/saas-admin/monitoring', requireAuth, async (req, res) => {
  try {
    const monitoringStats = {
      cpuUsage: 25.6,
      memoryUsage: 68.3,
      diskUsage: 42.1,
      activeConnections: 127,
      responseTime: 145,
      errorRate: 0.02
    };
    res.render('monitoring/index', { 
      title: '性能监控', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      monitoringStats
    });
  } catch (error) {
    console.error('监控页面错误:', error);
    res.render('monitoring/index', { 
      title: '性能监控', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      monitoringStats: { cpuUsage: 0, memoryUsage: 0, diskUsage: 0, activeConnections: 0, responseTime: 0, errorRate: 0 }
    });
  }
});

app.get('/logs', requireAuth, async (req, res) => {
  try {
    const logStats = {
      totalLogs: 15678,
      errorLogs: 23,
      warningLogs: 156,
      todayLogs: 1234
    };
    res.render('logs/index', { 
      title: '系统日志', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      logStats
    });
  } catch (error) {
    console.error('日志页面错误:', error);
    res.render('logs/index', { 
      title: '系统日志', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      logStats: { totalLogs: 0, errorLogs: 0, warningLogs: 0, todayLogs: 0 }
    });
  }
});

app.get('/saas-admin/logs', requireAuth, async (req, res) => {
  try {
    const logStats = {
      totalLogs: 15678,
      errorLogs: 23,
      warningLogs: 156,
      todayLogs: 1234,
      last24Hours: 892
    };
    const sourceStats = {
      'API接口': 5234,
      'Web界面': 3456,
      '定时任务': 2345,
      '系统核心': 1678,
      '第三方集成': 987,
      '其他': 567
    };
    res.render('logs/index', { 
      title: '系统日志', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      logStats,
      sourceStats
    });
  } catch (error) {
    console.error('日志页面错误:', error);
    res.render('logs/index', { 
      title: '系统日志', 
      admin: req.session.admin, 
      adminName: req.session.admin.name,
      logStats: { totalLogs: 0, errorLogs: 0, warningLogs: 0, todayLogs: 0, last24Hours: 0 },
      sourceStats: {}
    });
  }
});

// API路由 - 租户管理
app.get('/api/tenants/list', requireAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (status && status !== 'all') {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    if (search) {
      whereClause += ' AND (name LIKE ? OR tenant_code LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    const [tenants] = await db.query(`
      SELECT id, name, tenant_code, status, subscription_plan, user_count, 
             last_active, created_at, updated_at
      FROM tenant_info 
      WHERE ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ${Number(limit)} OFFSET ${Number(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM tenant_info WHERE ${whereClause}
    `, params);
    
    res.json({
      success: true,
      data: {
        tenants,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取租户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取租户列表失败',
      error: error.message
    });
  }
});

app.post('/api/tenants/create', requireAuth, async (req, res) => {
  try {
    const { name, tenant_code, contact_name, contact_phone, contact_email, subscription_plan } = req.body;
    
    if (!name || !tenant_code) {
      return res.status(400).json({
        success: false,
        message: '租户名称和代码不能为空'
      });
    }
    
    // 检查租户代码是否已存在
    const [existing] = await db.query('SELECT id FROM tenant_info WHERE tenant_code = ?', [tenant_code]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '租户代码已存在'
      });
    }
    
    const [result] = await db.query(`
      INSERT INTO tenant_info (
        name, tenant_code, contact_name, contact_phone, contact_email,
        subscription_plan, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)
    `, [name, tenant_code, contact_name, contact_phone, contact_email, subscription_plan || 'basic']);
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'create',
      'tenant',
      `创建租户: ${name} (${tenant_code})`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '创建租户成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建租户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建租户失败',
      error: error.message
    });
  }
});

app.put('/api/tenants/:id/status', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!['active', 'inactive', 'suspended'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }
    
    const [result] = await db.query(
      'UPDATE tenant_info SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, id]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '租户不存在'
      });
    }
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'update',
      'tenant',
      `修改租户状态: ID ${id} -> ${status}`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '状态更新成功'
    });
  } catch (error) {
    console.error('更新租户状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新状态失败',
      error: error.message
    });
  }
});

// API路由 - 用户管理
app.get('/api/users/list', requireAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, tenant_id, search } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (tenant_id && tenant_id !== 'all') {
      whereClause += ' AND u.tenant_id = ?';
      params.push(tenant_id);
    }
    
    if (search) {
      whereClause += ' AND (u.username LIKE ? OR u.nickname LIKE ? OR u.phone LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    const [users] = await db.query(`
      SELECT u.id, u.username, u.nickname, u.phone, u.email, u.status,
             u.last_login_time, u.created_at, t.name as tenant_name, t.tenant_code
      FROM users u
      LEFT JOIN tenant_info t ON u.tenant_id = t.id
      WHERE ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ${Number(limit)} OFFSET ${Number(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total 
      FROM users u
      LEFT JOIN tenant_info t ON u.tenant_id = t.id
      WHERE ${whereClause}
    `, params);
    
    res.json({
      success: true,
      data: {
        users,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
});

// 用户详情API
app.get('/api/users/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    
    const [users] = await db.query(`
      SELECT u.*, t.name as tenant_name, t.tenant_code
      FROM users u
      LEFT JOIN tenant_info t ON u.tenant_id = t.id
      WHERE u.id = ?
    `, [id]);
    
    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      data: users[0]
    });
  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户详情失败',
      error: error.message
    });
  }
});

// 创建用户API
app.post('/api/users/create', requireAuth, async (req, res) => {
  try {
    const { username, nickname, phone, email, tenant_id, password } = req.body;
    
    if (!username || !phone || !tenant_id) {
      return res.status(400).json({
        success: false,
        message: '用户名、手机号和租户不能为空'
      });
    }
    
    // 检查用户名是否已存在
    const [existing] = await db.query('SELECT id FROM users WHERE username = ? OR phone = ?', [username, phone]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '用户名或手机号已存在'
      });
    }
    
    // 生成默认密码
    const bcrypt = require('bcrypt');
    const defaultPassword = password || '123456';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);
    
    const [result] = await db.query(`
      INSERT INTO users (
        username, nickname, phone, email, tenant_id, password, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)
    `, [username, nickname || username, phone, email, tenant_id, hashedPassword]);
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'create',
      'user',
      `创建用户: ${username} (${phone})`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '创建用户成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
});

// 更新用户状态API
app.put('/api/users/:id/status', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!['active', 'inactive', 'banned'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }
    
    const [result] = await db.query(
      'UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, id]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'update',
      'user',
      `修改用户状态: ID ${id} -> ${status}`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '状态更新成功'
    });
  } catch (error) {
    console.error('更新用户状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新状态失败',
      error: error.message
    });
  }
});

// 重置用户密码API
app.put('/api/users/:id/reset-password', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;
    
    const password = newPassword || '123456';
    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const [result] = await db.query(
      'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [hashedPassword, id]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'update',
      'user',
      `重置用户密码: ID ${id}`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '密码重置成功',
      data: { newPassword: password }
    });
  } catch (error) {
    console.error('重置密码失败:', error);
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      error: error.message
    });
  }
});

// API路由 - 系统统计
app.get('/api/dashboard/stats', requireAuth, async (req, res) => {
  try {
    // 并行查询各种统计数据
    const [tenantStats] = await db.query(`
      SELECT 
        COUNT(*) as total_tenants,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_tenants,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_tenants,
        COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_tenants,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_new
      FROM tenant_info
    `);
    
    const [userStats] = await db.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN DATE(last_login_time) = CURDATE() THEN 1 END) as today_active_users,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_new_users
      FROM users
    `);
    
    // 获取最近7天的趋势数据
    const [trendData] = await db.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as new_tenants
      FROM tenant_info 
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `);
    
    // 获取订阅计划分布
    const [planDistribution] = await db.query(`
      SELECT 
        subscription_plan,
        COUNT(*) as count
      FROM tenant_info 
      WHERE status = 'active'
      GROUP BY subscription_plan
    `);
    
    res.json({
      success: true,
      data: {
        tenants: tenantStats[0],
        users: userStats[0],
        trends: trendData,
        planDistribution: planDistribution,
        systemUptime: process.uptime()
      }
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// API路由 - 订阅计划管理
app.get('/api/plans/list', requireAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (status && status !== 'all') {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    const [plans] = await db.query(`
      SELECT id, name, code, description, price_monthly, price_yearly, 
             features, user_limit, status, created_at, updated_at
      FROM subscription_plans
      WHERE ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ${Number(limit)} OFFSET ${Number(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM subscription_plans WHERE ${whereClause}
    `, params);
    
    res.json({
      success: true,
      data: {
        plans,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取订阅计划列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订阅计划列表失败',
      error: error.message
    });
  }
});

app.post('/api/plans/create', requireAuth, async (req, res) => {
  try {
    const { name, code, description, price_monthly, price_yearly, features, user_limit } = req.body;
    
    if (!name || !code || !price_monthly) {
      return res.status(400).json({
        success: false,
        message: '计划名称、代码和月费不能为空'
      });
    }
    
    // 检查计划代码是否已存在
    const [existing] = await db.query('SELECT id FROM subscription_plans WHERE code = ?', [code]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '计划代码已存在'
      });
    }
    
    const [result] = await db.query(`
      INSERT INTO subscription_plans (
        name, code, description, price_monthly, price_yearly, 
        features, user_limit, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)
    `, [name, code, description, parseFloat(price_monthly), parseFloat(price_yearly || 0), 
        JSON.stringify(features || []), parseInt(user_limit || 0)]);
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'create',
      'subscription_plan',
      `创建订阅计划: ${name} (${code})`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '创建订阅计划成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建订阅计划失败:', error);
    res.status(500).json({
      success: false,
      message: '创建订阅计划失败',
      error: error.message
    });
  }
});

app.put('/api/plans/:id/status', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!['active', 'inactive', 'deprecated'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }
    
    const [result] = await db.query(
      'UPDATE subscription_plans SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, id]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '订阅计划不存在'
      });
    }
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'update',
      'subscription_plan',
      `修改订阅计划状态: ID ${id} -> ${status}`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '状态更新成功'
    });
  } catch (error) {
    console.error('更新订阅计划状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新状态失败',
      error: error.message
    });
  }
});

// API路由 - 公告管理
app.get('/api/announcements/list', requireAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, status, type } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (status && status !== 'all') {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    if (type && type !== 'all') {
      whereClause += ' AND type = ?';
      params.push(type);
    }
    
    const [announcements] = await db.query(`
      SELECT id, title, content, type, status, priority, 
             target_audience, publish_time, created_at, updated_at
      FROM system_announcements
      WHERE ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ${Number(limit)} OFFSET ${Number(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM system_announcements WHERE ${whereClause}
    `, params);
    
    res.json({
      success: true,
      data: {
        announcements,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取公告列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公告列表失败',
      error: error.message
    });
  }
});

app.post('/api/announcements/create', requireAuth, async (req, res) => {
  try {
    const { title, content, type, priority, target_audience, publish_time } = req.body;
    
    if (!title || !content || !type) {
      return res.status(400).json({
        success: false,
        message: '标题、内容和类型不能为空'
      });
    }
    
    const [result] = await db.query(`
      INSERT INTO system_announcements (
        title, content, type, priority, target_audience, 
        publish_time, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [title, content, type, priority || 'normal', 
        JSON.stringify(target_audience || 'all'), publish_time || new Date()]);
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'create',
      'announcement',
      `创建公告: ${title}`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '创建公告成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建公告失败:', error);
    res.status(500).json({
      success: false,
      message: '创建公告失败',
      error: error.message
    });
  }
});

// API路由 - AI配置管理
app.get('/api/ai-config/list', requireAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, provider, status } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (provider && provider !== 'all') {
      whereClause += ' AND provider = ?';
      params.push(provider);
    }
    
    if (status && status !== 'all') {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    const [configs] = await db.query(`
      SELECT id, provider, model_name, api_key, api_endpoint, 
             config_params, status, created_at, updated_at
      FROM ai_configurations
      WHERE ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ${Number(limit)} OFFSET ${Number(offset)}
    `, params);
    
    // 隐藏敏感信息
    const safeConfigs = configs.map(config => ({
      ...config,
      api_key: config.api_key ? config.api_key.replace(/.(?=.{4})/g, '*') : ''
    }));
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM ai_configurations WHERE ${whereClause}
    `, params);
    
    res.json({
      success: true,
      data: {
        configs: safeConfigs,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取AI配置列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取AI配置列表失败',
      error: error.message
    });
  }
});

app.post('/api/ai-config/create', requireAuth, async (req, res) => {
  try {
    const { provider, model_name, api_key, api_endpoint, config_params } = req.body;
    
    if (!provider || !model_name || !api_key) {
      return res.status(400).json({
        success: false,
        message: '服务商、模型名称和API密钥不能为空'
      });
    }
    
    const [result] = await db.query(`
      INSERT INTO ai_configurations (
        provider, model_name, api_key, api_endpoint, 
        config_params, status, created_at
      ) VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)
    `, [provider, model_name, api_key, api_endpoint || '', 
        JSON.stringify(config_params || {})]);
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'create',
      'ai_config',
      `创建AI配置: ${provider} - ${model_name}`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '创建AI配置成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建AI配置失败:', error);
    res.status(500).json({
      success: false,
      message: '创建AI配置失败',
      error: error.message
    });
  }
});

// API路由 - 知识库管理
app.get('/api/knowledge/list', requireAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, category, status } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (category && category !== 'all') {
      whereClause += ' AND category = ?';
      params.push(category);
    }
    
    if (status && status !== 'all') {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    const [documents] = await db.query(`
      SELECT id, title, category, content_summary, tags, 
             view_count, status, created_at, updated_at
      FROM knowledge_base
      WHERE ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ${Number(limit)} OFFSET ${Number(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM knowledge_base WHERE ${whereClause}
    `, params);
    
    res.json({
      success: true,
      data: {
        documents,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取知识库列表失败',
      error: error.message
    });
  }
});

app.post('/api/knowledge/create', requireAuth, async (req, res) => {
  try {
    const { title, category, content, tags } = req.body;
    
    if (!title || !category || !content) {
      return res.status(400).json({
        success: false,
        message: '标题、分类和内容不能为空'
      });
    }
    
    const content_summary = content.length > 200 ? content.substring(0, 200) + '...' : content;
    
    const [result] = await db.query(`
      INSERT INTO knowledge_base (
        title, category, content, content_summary, tags,
        view_count, status, created_at
      ) VALUES (?, ?, ?, ?, ?, 0, 'active', CURRENT_TIMESTAMP)
    `, [title, category, content, content_summary, 
        JSON.stringify(tags || [])]);
    
    // 记录操作日志
    await db.query(`
      INSERT INTO operation_logs (
        operator_type, operator_id, operation_type, operation_module,
        operation_desc, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'saas_admin',
      req.session.admin.id,
      'create',
      'knowledge',
      `创建知识文档: ${title}`,
      req.ip,
      req.get('User-Agent')
    ]);
    
    res.json({
      success: true,
      message: '创建知识文档成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('创建知识文档失败:', error);
    res.status(500).json({
      success: false,
      message: '创建知识文档失败',
      error: error.message
    });
  }
});

// API路由 - 系统监控
app.get('/api/monitoring/stats', requireAuth, async (req, res) => {
  try {
    const os = require('os');
    
    const stats = {
      system: {
        platform: os.platform(),
        arch: os.arch(),
        uptime: os.uptime(),
        loadavg: os.loadavg(),
        totalmem: os.totalmem(),
        freemem: os.freemem()
      },
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      timestamp: new Date().toISOString()
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取监控数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取监控数据失败',
      error: error.message
    });
  }
});

// API路由 - 系统日志
app.get('/api/logs/list', requireAuth, async (req, res) => {
  try {
    const { page = 1, limit = 20, level, module, start_date, end_date } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (level && level !== 'all') {
      whereClause += ' AND operation_type = ?';
      params.push(level);
    }
    
    if (module && module !== 'all') {
      whereClause += ' AND operation_module = ?';
      params.push(module);
    }
    
    if (start_date) {
      whereClause += ' AND created_at >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND created_at <= ?';
      params.push(end_date);
    }
    
    const [logs] = await db.query(`
      SELECT id, operator_type, operator_id, operation_type, 
             operation_module, operation_desc, ip_address, 
             user_agent, created_at
      FROM operation_logs
      WHERE ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ${Number(limit)} OFFSET ${Number(offset)}
    `, params);
    
    const [countResult] = await db.query(`
      SELECT COUNT(*) as total FROM operation_logs WHERE ${whereClause}
    `, params);
    
    res.json({
      success: true,
      data: {
        logs,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取系统日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统日志失败',
      error: error.message
    });
  }
});

// Dashboard路由
app.get('/saas-admin/dashboard', requireAuth, (req, res) => {
  res.render('dashboard/index', {
    title: '仪表盘 - SAAS管理后台',
    admin: req.session.admin,
    adminName: req.session.admin.name
  });
});

// 退出登录
app.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({
        success: false,
        message: '退出登录失败'
      });
    }
    res.json({
      success: true,
      message: '退出登录成功',
      redirect: '/'
    });
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'SAAS管理后台API端点不存在',
    path: req.originalUrl
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('SAAS管理后台服务器错误:', error);
  
  // CORS错误
  if (error.message === 'Not allowed by CORS') {
    return res.status(403).json({
      success: false,
      message: '跨域请求被拒绝'
    });
  }
  
  // JSON解析错误
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    return res.status(400).json({
      success: false,
      message: '请求数据格式错误'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'SAAS管理后台服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 启动服务器
const startAdminServer = async () => {
  try {
    // 初始化数据库
    const dbInitialized = await db.initDatabase();
    if (!dbInitialized) {
      console.error('数据库初始化失败，SAAS管理后台无法启动');
      process.exit(1);
    }

    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 智慧养鹅SAAS管理后台已启动`);
      console.log(`📡 监听端口: ${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
      
      if (process.env.NODE_ENV !== 'production') {
        console.log(`🔗 健康检查: http://localhost:${PORT}/api/health`);
        console.log(`🏠 管理后台首页: http://localhost:${PORT}/`);
        console.log(`📊 仪表盘: http://localhost:${PORT}/dashboard`);
      }
    });

    // 保存server实例用于优雅关闭
    global.adminServer = server;

  } catch (error) {
    console.error('启动SAAS管理后台失败:', error);
    process.exit(1);
  }
};

// 优雅关闭处理
process.on('SIGTERM', async () => {
  console.log('收到SIGTERM信号，准备关闭SAAS管理后台...');
  
  if (global.adminServer) {
    global.adminServer.close(async () => {
      console.log('SAAS管理后台HTTP服务器已关闭');
      
      // 关闭数据库连接
      await db.closePool();
      
      console.log('SAAS管理后台优雅关闭完成');
      process.exit(0);
    });
  }
});

process.on('SIGINT', async () => {
  console.log('收到SIGINT信号，准备关闭SAAS管理后台...');
  
  if (global.adminServer) {
    global.adminServer.close(async () => {
      console.log('SAAS管理后台HTTP服务器已关闭');
      
      // 关闭数据库连接
      await db.closePool();
      
      console.log('SAAS管理后台优雅关闭完成');
      process.exit(0);
    });
  }
});

// 启动应用
startAdminServer();

module.exports = app;